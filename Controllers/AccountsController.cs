using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using HSU_NguyenHoangThanhSang_22207613.Data;
using HSU_NguyenHoangThanhSang_22207613.Models;
using Microsoft.AspNetCore.Authorization;

namespace HSU_NguyenHoangThanhSang_22207613.Controllers
{
    public class AccountsController : BaseController
    {
        private readonly ApplicationDbContext _context;

        public AccountsController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Accounts
        public async Task<IActionResult> Index()
        {
            var accounts = await _context.Accounts
                .Include(a => a.Group)
                .Where(a => a.IsActive)
                .OrderBy(a => a.FirstName)
                .ThenBy(a => a.LastName)
                .ToListAsync();
            return View(accounts);
        }

        // GET: Accounts/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var account = await _context.Accounts
                .Include(a => a.Group)
                .Include(a => a.Orders)
                .FirstOrDefaultAsync(m => m.Id == id);
            
            if (account == null)
            {
                return NotFound();
            }

            return View(account);
        }

        // GET: Accounts/CreateOrEdit
        public async Task<IActionResult> CreateOrEdit(int? id)
        {
            await PopulateGroupsDropDown();
            
            if (id == null)
            {
                // Create new
                return View(new Account());
            }
            else
            {
                // Edit existing
                var account = await _context.Accounts.FindAsync(id);
                if (account == null)
                {
                    return NotFound();
                }
                return View(account);
            }
        }

        // POST: Accounts/CreateOrEdit
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateOrEdit(Account account)
        {
            // Remove navigation property validation
            ModelState.Remove("Group");
            ModelState.Remove("Orders");

            // For create mode, password is required
            if (account.Id == 0 && string.IsNullOrEmpty(account.Password))
            {
                ModelState.AddModelError("Password", "Mật khẩu là bắt buộc khi tạo tài khoản mới.");
            }

            // For edit mode, if password is empty, don't validate it
            if (account.Id != 0 && string.IsNullOrEmpty(account.Password))
            {
                ModelState.Remove("Password");
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Check for duplicate username/email
                    var existingAccount = await _context.Accounts
                        .Where(a => a.Id != account.Id && (a.UserName == account.UserName || a.Email == account.Email))
                        .FirstOrDefaultAsync();

                    if (existingAccount != null)
                    {
                        if (existingAccount.UserName == account.UserName)
                        {
                            ModelState.AddModelError("UserName", "Tên đăng nhập đã tồn tại.");
                        }
                        if (existingAccount.Email == account.Email)
                        {
                            ModelState.AddModelError("Email", "Email đã tồn tại.");
                        }
                        await PopulateGroupsDropDown(account.GroupId);
                        return View(account);
                    }

                    if (account.Id == 0)
                    {
                        // Create new
                        account.CreatedDate = DateTime.Now;
                        _context.Add(account);
                        TempData["SuccessMessage"] = "Tài khoản đã được tạo thành công!";
                    }
                    else
                    {
                        // Update existing - handle password
                        var existingEntity = await _context.Accounts.FindAsync(account.Id);
                        if (existingEntity != null)
                        {
                            existingEntity.FirstName = account.FirstName;
                            existingEntity.LastName = account.LastName;
                            existingEntity.Email = account.Email;
                            existingEntity.UserName = account.UserName;
                            existingEntity.GroupId = account.GroupId;
                            existingEntity.IsActive = account.IsActive;

                            // Only update password if provided
                            if (!string.IsNullOrEmpty(account.Password))
                            {
                                existingEntity.Password = account.Password;
                            }
                        }
                        TempData["SuccessMessage"] = "Tài khoản đã được cập nhật thành công!";
                    }

                    await _context.SaveChangesAsync();
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = "Có lỗi xảy ra: " + ex.Message;
                }
            }

            await PopulateGroupsDropDown(account.GroupId);
            return View(account);
        }

        // GET: Accounts/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var account = await _context.Accounts
                .FirstOrDefaultAsync(m => m.Id == id);
            
            if (account == null)
            {
                return NotFound();
            }

            // Check if account has any orders
            var hasOrders = await _context.Orders.AnyAsync(o => o.AccountId == id);
            if (hasOrders)
            {
                TempData["ErrorMessage"] = "Không thể xóa tài khoản này vì đang có đơn hàng liên quan!";
                return RedirectToAction(nameof(Index));
            }

            // Soft delete
            account.IsActive = false;
            _context.Update(account);
            await _context.SaveChangesAsync();
            
            TempData["SuccessMessage"] = "Tài khoản đã được xóa thành công!";
            return RedirectToAction(nameof(Index));
        }

        private async Task PopulateGroupsDropDown(object? selectedGroup = null)
        {
            var groups = await _context.Groups
                .Where(g => g.IsActive)
                .OrderBy(g => g.Name)
                .ToListAsync();
            
            ViewBag.GroupId = new SelectList(groups, "Id", "Name", selectedGroup);
        }



        private bool AccountExists(int id)
        {
            return _context.Accounts.Any(e => e.Id == id);
        }
    }
}
