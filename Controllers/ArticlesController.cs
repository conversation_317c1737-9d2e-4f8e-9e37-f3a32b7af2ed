using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using HSU_NguyenHoangThanhSang_22207613.Data;
using HSU_NguyenHoangThanhSang_22207613.Models;

namespace HSU_NguyenHoangThanhSang_22207613.Controllers
{
    public class ArticlesController : BaseController
    {
        private readonly ApplicationDbContext _context;

        public ArticlesController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Articles
        public async Task<IActionResult> Index(string searchTerm)
        {
            var query = _context.Articles
                .Include(a => a.Category)
                .Where(a => a.IsActive);

            // Apply search filter if search term is provided
            if (!string.IsNullOrEmpty(searchTerm))
            {
                searchTerm = searchTerm.Trim().ToLower();
                query = query.Where(a =>
                    a.Title.ToLower().Contains(searchTerm) ||
                    (a.Content != null && a.Content.ToLower().Contains(searchTerm)) ||
                    (a.Summary != null && a.Summary.ToLower().Contains(searchTerm)) ||
                    a.Category.Name.ToLower().Contains(searchTerm)
                );
            }

            var articles = await query
                .OrderByDescending(a => a.CreatedDate)
                .ToListAsync();

            // Pass search term to view for display
            ViewBag.SearchTerm = searchTerm;
            ViewBag.SearchPlaceholder = "Tìm theo tiêu đề, nội dung, danh mục...";

            return View(articles);
        }

        // GET: Articles/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var article = await _context.Articles
                .Include(a => a.Category)
                .FirstOrDefaultAsync(m => m.Id == id);
            
            if (article == null)
            {
                return NotFound();
            }

            return View(article);
        }

        // GET: Articles/CreateOrEdit
        public async Task<IActionResult> CreateOrEdit(int? id)
        {
            await PopulateCategoriesDropDown();
            
            if (id == null)
            {
                // Create new
                return View(new Article());
            }
            else
            {
                // Edit existing
                var article = await _context.Articles.FindAsync(id);
                if (article == null)
                {
                    return NotFound();
                }
                return View(article);
            }
        }

        // POST: Articles/CreateOrEdit
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateOrEdit(Article article)
        {
            // Remove navigation property validation
            ModelState.Remove("Category");

            if (ModelState.IsValid)
            {
                try
                {
                    // Check for duplicate code
                    var existingArticle = await _context.Articles
                        .Where(a => a.Id != article.Id && a.Code == article.Code)
                        .FirstOrDefaultAsync();

                    if (existingArticle != null)
                    {
                        ModelState.AddModelError("Code", "Mã bài viết đã tồn tại.");
                        await PopulateCategoriesDropDown(article.CategoryId);
                        return View(article);
                    }

                    if (article.Id == 0)
                    {
                        // Create new
                        article.CreatedDate = DateTime.Now;
                        _context.Add(article);
                        TempData["SuccessMessage"] = "Bài viết đã được tạo thành công!";
                    }
                    else
                    {
                        // Update existing
                        _context.Update(article);
                        TempData["SuccessMessage"] = "Bài viết đã được cập nhật thành công!";
                    }
                    
                    await _context.SaveChangesAsync();
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = "Có lỗi xảy ra: " + ex.Message;
                }
            }
            
            await PopulateCategoriesDropDown(article.CategoryId);
            return View(article);
        }

        // GET: Articles/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var article = await _context.Articles
                .FirstOrDefaultAsync(m => m.Id == id);
            
            if (article == null)
            {
                return NotFound();
            }

            // Soft delete
            article.IsActive = false;
            _context.Update(article);
            await _context.SaveChangesAsync();
            
            TempData["SuccessMessage"] = "Bài viết đã được xóa thành công!";
            return RedirectToAction(nameof(Index));
        }

        private async Task PopulateCategoriesDropDown(object? selectedCategory = null)
        {
            var categories = await _context.Categories
                .Where(c => c.IsActive)
                .OrderBy(c => c.Name)
                .ToListAsync();
            
            ViewBag.CategoryId = new SelectList(categories, "Id", "Name", selectedCategory);
        }

        private bool ArticleExists(int id)
        {
            return _context.Articles.Any(e => e.Id == id);
        }
    }
}
