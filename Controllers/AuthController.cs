using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using HSU_NguyenHoangThanhSang_22207613.Data;
using HSU_NguyenHoangThanhSang_22207613.Models;

namespace HSU_NguyenHoangThanhSang_22207613.Controllers
{
    public class AuthController : Controller
    {
        private readonly ApplicationDbContext _context;

        public AuthController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public IActionResult Login()
        {
            // If user is already logged in, redirect to home
            if (HttpContext.Session.GetString("UserId") != null)
            {
                return RedirectToAction("Index", "Home");
            }
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> Login(string username, string password)
        {
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                ViewBag.Error = "Vui lòng nhập đầy đủ thông tin đăng nhập.";
                return View();
            }

            var account = await _context.Accounts
                .Include(a => a.Group)
                .FirstOrDefaultAsync(a => a.UserName == username && a.Password == password && a.IsActive);

            if (account != null)
            {
                // Store user information in session
                HttpContext.Session.SetString("UserId", account.Id.ToString());
                HttpContext.Session.SetString("UserName", account.UserName);
                HttpContext.Session.SetString("FullName", $"{account.FirstName} {account.LastName}");
                HttpContext.Session.SetString("GroupName", account.Group.Name);
                HttpContext.Session.SetString("GroupCode", account.Group.Code);

                return RedirectToAction("Index", "Home");
            }
            else
            {
                ViewBag.Error = "Tên đăng nhập hoặc mật khẩu không đúng.";
                return View();
            }
        }

        [HttpPost]
        public IActionResult Logout()
        {
            HttpContext.Session.Clear();
            return RedirectToAction("Login");
        }

        // Check if user is logged in
        private bool IsUserLoggedIn()
        {
            return HttpContext.Session.GetString("UserId") != null;
        }

        // Check if user is admin
        private bool IsUserAdmin()
        {
            var groupCode = HttpContext.Session.GetString("GroupCode");
            return groupCode == "ADMIN";
        }
    }
}
