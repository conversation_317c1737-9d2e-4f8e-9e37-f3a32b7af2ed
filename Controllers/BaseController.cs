using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace HSU_NguyenHoangThanhSang_22207613.Controllers
{
    public class BaseController : Controller
    {
        public override void OnActionExecuting(ActionExecutingContext context)
        {
            // Check if user is logged in
            if (HttpContext.Session.GetString("UserId") == null)
            {
                context.Result = RedirectToAction("Login", "Auth");
                return;
            }

            // Pass user information to ViewBag for layout
            ViewBag.UserName = HttpContext.Session.GetString("UserName");
            ViewBag.FullName = HttpContext.Session.GetString("FullName");
            ViewBag.GroupName = HttpContext.Session.GetString("GroupName");
            ViewBag.GroupCode = HttpContext.Session.GetString("GroupCode");
            ViewBag.IsAdmin = HttpContext.Session.GetString("GroupCode") == "ADMIN";

            base.OnActionExecuting(context);
        }

        protected bool IsUserAdmin()
        {
            return HttpContext.Session.GetString("GroupCode") == "ADMIN";
        }

        protected int GetCurrentUserId()
        {
            var userIdString = HttpContext.Session.GetString("UserId");
            return int.TryParse(userIdString, out int userId) ? userId : 0;
        }
    }
}
