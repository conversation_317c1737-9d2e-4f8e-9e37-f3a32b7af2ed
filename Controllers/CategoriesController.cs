using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using HSU_NguyenHoangThanhSang_22207613.Data;
using HSU_NguyenHoangThanhSang_22207613.Models;

namespace HSU_NguyenHoangThanhSang_22207613.Controllers
{
    public class CategoriesController : BaseController
    {
        private readonly ApplicationDbContext _context;

        public CategoriesController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Categories
        public async Task<IActionResult> Index()
        {
            var categories = await _context.Categories
                .Where(c => c.IsActive)
                .OrderBy(c => c.Name)
                .ToListAsync();
            return View(categories);
        }

        // GET: Categories/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var category = await _context.Categories
                .Include(c => c.Articles.Where(a => a.IsActive))
                .Include(c => c.Products.Where(p => p.IsActive))
                .FirstOrDefaultAsync(m => m.Id == id);
            
            if (category == null)
            {
                return NotFound();
            }

            return View(category);
        }

        // GET: Categories/CreateOrEdit
        public IActionResult CreateOrEdit(int? id)
        {
            if (id == null)
            {
                // Create new
                return View(new Category());
            }
            else
            {
                // Edit existing
                var category = _context.Categories.Find(id);
                if (category == null)
                {
                    return NotFound();
                }
                return View(category);
            }
        }

        // POST: Categories/CreateOrEdit
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateOrEdit(Category category)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    // Check for duplicate code
                    var existingCategory = await _context.Categories
                        .Where(c => c.Id != category.Id && c.Code == category.Code)
                        .FirstOrDefaultAsync();

                    if (existingCategory != null)
                    {
                        ModelState.AddModelError("Code", "Mã danh mục đã tồn tại.");
                        return View(category);
                    }

                    if (category.Id == 0)
                    {
                        // Create new
                        category.CreatedDate = DateTime.Now;
                        _context.Add(category);
                        TempData["SuccessMessage"] = "Danh mục đã được tạo thành công!";
                    }
                    else
                    {
                        // Update existing
                        _context.Update(category);
                        TempData["SuccessMessage"] = "Danh mục đã được cập nhật thành công!";
                    }
                    
                    await _context.SaveChangesAsync();
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = "Có lỗi xảy ra: " + ex.Message;
                }
            }
            return View(category);
        }

        // GET: Categories/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var category = await _context.Categories
                .FirstOrDefaultAsync(m => m.Id == id);
            
            if (category == null)
            {
                return NotFound();
            }

            // Check if category has any articles or products
            var hasArticles = await _context.Articles.AnyAsync(a => a.CategoryId == id);
            var hasProducts = await _context.Products.AnyAsync(p => p.CategoryId == id);
            
            if (hasArticles || hasProducts)
            {
                TempData["ErrorMessage"] = "Không thể xóa danh mục này vì đang có bài viết hoặc sản phẩm liên quan!";
                return RedirectToAction(nameof(Index));
            }

            // Soft delete
            category.IsActive = false;
            _context.Update(category);
            await _context.SaveChangesAsync();
            
            TempData["SuccessMessage"] = "Danh mục đã được xóa thành công!";
            return RedirectToAction(nameof(Index));
        }

        private bool CategoryExists(int id)
        {
            return _context.Categories.Any(e => e.Id == id);
        }
    }
}
