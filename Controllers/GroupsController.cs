using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using HSU_NguyenHoangThanhSang_22207613.Data;
using HSU_NguyenHoangThanhSang_22207613.Models;

namespace HSU_NguyenHoangThanhSang_22207613.Controllers
{
    public class GroupsController : BaseController
    {
        private readonly ApplicationDbContext _context;

        public GroupsController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Groups
        public async Task<IActionResult> Index()
        {
            var groups = await _context.Groups
                .Where(g => g.IsActive)
                .OrderBy(g => g.Name)
                .ToListAsync();
            return View(groups);
        }

        // GET: Groups/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var group = await _context.Groups
                .Include(g => g.Accounts)
                .FirstOrDefaultAsync(m => m.Id == id);
            
            if (group == null)
            {
                return NotFound();
            }

            return View(group);
        }

        // GET: Groups/CreateOrEdit
        public IActionResult CreateOrEdit(int? id)
        {
            if (id == null)
            {
                // Create new
                return View(new Group());
            }
            else
            {
                // Edit existing
                var group = _context.Groups.Find(id);
                if (group == null)
                {
                    return NotFound();
                }
                return View(group);
            }
        }

        // POST: Groups/CreateOrEdit
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateOrEdit(Group group)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    if (group.Id == 0)
                    {
                        // Create new
                        group.CreatedDate = DateTime.Now;
                        _context.Add(group);
                        TempData["SuccessMessage"] = "Nhóm quyền đã được tạo thành công!";
                    }
                    else
                    {
                        // Update existing
                        _context.Update(group);
                        TempData["SuccessMessage"] = "Nhóm quyền đã được cập nhật thành công!";
                    }
                    
                    await _context.SaveChangesAsync();
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = "Có lỗi xảy ra: " + ex.Message;
                }
            }
            return View(group);
        }

        // GET: Groups/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var group = await _context.Groups
                .FirstOrDefaultAsync(m => m.Id == id);
            
            if (group == null)
            {
                return NotFound();
            }

            // Check if group has any accounts
            var hasAccounts = await _context.Accounts.AnyAsync(a => a.GroupId == id);
            if (hasAccounts)
            {
                TempData["ErrorMessage"] = "Không thể xóa nhóm quyền này vì đang có tài khoản sử dụng!";
                return RedirectToAction(nameof(Index));
            }

            // Soft delete
            group.IsActive = false;
            _context.Update(group);
            await _context.SaveChangesAsync();
            
            TempData["SuccessMessage"] = "Nhóm quyền đã được xóa thành công!";
            return RedirectToAction(nameof(Index));
        }

        private bool GroupExists(int id)
        {
            return _context.Groups.Any(e => e.Id == id);
        }
    }
}
