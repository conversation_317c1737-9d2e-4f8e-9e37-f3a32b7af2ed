using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using HSU_NguyenHoangThanhSang_22207613.Models;
using HSU_NguyenHoangThanhSang_22207613.Models.ViewModels;
using HSU_NguyenHoangThanhSang_22207613.Data;

namespace HSU_NguyenHoangThanhSang_22207613.Controllers;

public class HomeController : BaseController
{
    private readonly ILogger<HomeController> _logger;
    private readonly ApplicationDbContext _context;

    public HomeController(ILogger<HomeController> logger, ApplicationDbContext context)
    {
        _logger = logger;
        _context = context;
    }

    public async Task<IActionResult> Index()
    {
        // Tính toán thống kê từ database
        var viewModel = new DashboardViewModel
        {
            TotalOrders = await _context.Orders.Where(o => o.IsActive).CountAsync(),
            TotalProducts = await _context.Products.Where(p => p.IsActive).CountAsync(),
            TotalCustomers = await _context.Accounts.Where(a => a.IsActive).CountAsync(),
            TotalCategories = await _context.Categories.Where(c => c.IsActive).CountAsync(),

            // Thống kê doanh thu
            TotalRevenue = await _context.OrderDetails
                .Where(od => od.IsActive && od.Order.IsActive)
                .SumAsync(od => od.Product.Price * od.Quantity),

            // Đơn hàng hôm nay
            TodayOrders = await _context.Orders
                .Where(o => o.IsActive && o.OrderDate.Date == DateTime.Today)
                .CountAsync(),

            // Sản phẩm bán chạy nhất
            TopSellingProducts = await _context.OrderDetails
                .Where(od => od.IsActive && od.Order.IsActive)
                .GroupBy(od => new { od.ProductId, od.Product.Name })
                .Select(g => new TopSellingProductViewModel
                {
                    ProductName = g.Key.Name,
                    TotalSold = g.Sum(od => od.Quantity)
                })
                .OrderByDescending(x => x.TotalSold)
                .Take(5)
                .ToListAsync(),

            // Đơn hàng gần đây
            RecentOrders = await _context.Orders
                .Include(o => o.Account)
                .Include(o => o.OrderStatus)
                .Where(o => o.IsActive)
                .OrderByDescending(o => o.CreatedDate)
                .Take(5)
                .ToListAsync()
        };

        return View(viewModel);
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
