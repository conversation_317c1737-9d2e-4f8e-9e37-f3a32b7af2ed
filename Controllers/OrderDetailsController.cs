using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using HSU_NguyenHoangThanhSang_22207613.Data;
using HSU_NguyenHoangThanhSang_22207613.Models;

namespace HSU_NguyenHoangThanhSang_22207613.Controllers
{
    public class OrderDetailsController : BaseController
    {
        private readonly ApplicationDbContext _context;

        public OrderDetailsController(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            var orderDetails = await _context.OrderDetails
                .Include(od => od.Order)
                    .ThenInclude(o => o.Account)
                .Include(od => od.Product)
                    .ThenInclude(p => p.Category)
                .Where(od => od.IsActive)
                .OrderByDescending(od => od.CreatedDate)
                .ToListAsync();
            return View(orderDetails);
        }

        public async Task<IActionResult> Details(int? id)
        {
            if (id == null) return NotFound();
            var orderDetail = await _context.OrderDetails
                .Include(od => od.Order)
                    .ThenInclude(o => o.Account)
                .Include(od => od.Order)
                    .ThenInclude(o => o.OrderStatus)
                .Include(od => od.Product)
                    .ThenInclude(p => p.Category)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (orderDetail == null) return NotFound();
            return View(orderDetail);
        }
    }
}
