using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using HSU_NguyenHoangThanhSang_22207613.Data;
using HSU_NguyenHoangThanhSang_22207613.Models;

namespace HSU_NguyenHoangThanhSang_22207613.Controllers
{
    public class OrderStatusesController : BaseController
    {
        private readonly ApplicationDbContext _context;

        public OrderStatusesController(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            var orderStatuses = await _context.OrderStatuses.Where(os => os.IsActive).OrderBy(os => os.Name).ToListAsync();
            return View(orderStatuses);
        }

        public async Task<IActionResult> Details(int? id)
        {
            if (id == null) return NotFound();
            var orderStatus = await _context.OrderStatuses
                .Include(os => os.Orders.Where(o => o.IsActive))
                    .ThenInclude(o => o.Account)
                .Include(os => os.Orders)
                    .ThenInclude(o => o.OrderDetails)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (orderStatus == null) return NotFound();
            return View(orderStatus);
        }

        public IActionResult CreateOrEdit(int? id)
        {
            if (id == null) return View(new OrderStatus());
            var orderStatus = _context.OrderStatuses.Find(id);
            if (orderStatus == null) return NotFound();
            return View(orderStatus);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateOrEdit(OrderStatus orderStatus)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    var existing = await _context.OrderStatuses.Where(os => os.Id != orderStatus.Id && os.Code == orderStatus.Code).FirstOrDefaultAsync();
                    if (existing != null)
                    {
                        ModelState.AddModelError("Code", "Mã trạng thái đơn hàng đã tồn tại.");
                        return View(orderStatus);
                    }

                    if (orderStatus.Id == 0)
                    {
                        orderStatus.CreatedDate = DateTime.Now;
                        _context.Add(orderStatus);
                        TempData["SuccessMessage"] = "Trạng thái đơn hàng đã được tạo thành công!";
                    }
                    else
                    {
                        _context.Update(orderStatus);
                        TempData["SuccessMessage"] = "Trạng thái đơn hàng đã được cập nhật thành công!";
                    }
                    await _context.SaveChangesAsync();
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = "Có lỗi xảy ra: " + ex.Message;
                }
            }
            return View(orderStatus);
        }

        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null) return NotFound();
            var orderStatus = await _context.OrderStatuses.FirstOrDefaultAsync(m => m.Id == id);
            if (orderStatus == null) return NotFound();

            var hasOrders = await _context.Orders.AnyAsync(o => o.OrderStatusId == id);
            if (hasOrders)
            {
                TempData["ErrorMessage"] = "Không thể xóa trạng thái đơn hàng này vì đang có đơn hàng liên quan!";
                return RedirectToAction(nameof(Index));
            }

            orderStatus.IsActive = false;
            _context.Update(orderStatus);
            await _context.SaveChangesAsync();
            TempData["SuccessMessage"] = "Trạng thái đơn hàng đã được xóa thành công!";
            return RedirectToAction(nameof(Index));
        }
    }
}
