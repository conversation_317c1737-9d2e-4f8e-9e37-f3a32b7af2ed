using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using HSU_NguyenHoangThanhSang_22207613.Data;
using HSU_NguyenHoangThanhSang_22207613.Models;
using HSU_NguyenHoangThanhSang_22207613.Models.ViewModels;

namespace HSU_NguyenHoangThanhSang_22207613.Controllers
{
    public class OrdersController : BaseController
    {
        private readonly ApplicationDbContext _context;

        public OrdersController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Orders
        public async Task<IActionResult> Index()
        {
            var orders = await _context.Orders
                .Include(o => o.Account)
                .Include(o => o.OrderStatus)
                .Include(o => o.OrderDetails)
                    .ThenInclude(od => od.Product)
                .OrderByDescending(o => o.CreatedDate)
                .ThenByDescending(o => o.IsActive)
                .ToListAsync();
            return View(orders);
        }

        // GET: Orders/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var order = await _context.Orders
                .Include(o => o.Account)
                .Include(o => o.OrderStatus)
                .Include(o => o.OrderDetails)
                    .ThenInclude(od => od.Product)
                    .ThenInclude(p => p.Category)
                .FirstOrDefaultAsync(m => m.Id == id);
            
            if (order == null)
            {
                return NotFound();
            }

            await PopulateOrderStatusDropDown(order.OrderStatusId);
            return View(order);
        }

        // GET: Orders/CreateOrEdit
        public async Task<IActionResult> CreateOrEdit(int? id)
        {
            await PopulateDropDowns();

            if (id == null)
            {
                // Create new
                return View(new Order());
            }
            else
            {
                // Edit existing
                var order = await _context.Orders
                    .Include(o => o.OrderDetails)
                    .FirstOrDefaultAsync(o => o.Id == id);
                if (order == null)
                {
                    return NotFound();
                }
                return View(order);
            }
        }

        // GET: Orders/CreateWithDetails
        public async Task<IActionResult> CreateWithDetails()
        {
            await PopulateDropDownsForCreate();
            return View(new OrderCreateViewModel());
        }

        // POST: Orders/CreateWithDetails
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateWithDetails(OrderCreateViewModel model)
        {
            // Remove navigation property validation
            ModelState.Remove("OrderDetails");

            if (ModelState.IsValid)
            {
                try
                {
                    // Create order
                    var order = new Order
                    {
                        AccountId = model.AccountId,
                        OrderDate = model.OrderDate,
                        OrderStatusId = model.OrderStatusId,
                        IsActive = model.IsActive,
                        CreatedDate = DateTime.Now
                    };

                    _context.Orders.Add(order);
                    await _context.SaveChangesAsync();

                    // Create order details
                    if (model.OrderDetails != null && model.OrderDetails.Any())
                    {
                        foreach (var detail in model.OrderDetails.Where(d => d.ProductId > 0 && d.Quantity > 0))
                        {
                            var orderDetail = new OrderDetail
                            {
                                OrderId = order.Id,
                                ProductId = detail.ProductId,
                                Quantity = detail.Quantity,
                                IsActive = true,
                                CreatedDate = DateTime.Now
                            };
                            _context.OrderDetails.Add(orderDetail);
                        }
                    }

                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "Đơn hàng và chi tiết đã được tạo thành công!";
                    return RedirectToAction("Details", new { id = order.Id });
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = "Có lỗi xảy ra: " + ex.Message;
                }
            }

            await PopulateDropDownsForCreate();
            return View(model);
        }

        // POST: Orders/CreateOrEdit
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateOrEdit(Order order)
        {
            // Remove navigation property validation
            ModelState.Remove("Account");
            ModelState.Remove("OrderStatus");
            ModelState.Remove("OrderDetails");

            if (ModelState.IsValid)
            {
                try
                {
                    if (order.Id == 0)
                    {
                        // Create new
                        order.CreatedDate = DateTime.Now;
                        _context.Add(order);
                        TempData["SuccessMessage"] = "Đơn hàng đã được tạo thành công!";
                    }
                    else
                    {
                        // Update existing
                        _context.Update(order);
                        TempData["SuccessMessage"] = "Đơn hàng đã được cập nhật thành công!";
                    }
                    
                    await _context.SaveChangesAsync();
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = "Có lỗi xảy ra: " + ex.Message;
                }
            }
            
            await PopulateDropDowns(order.AccountId, order.OrderStatusId);
            return View(order);
        }

        // POST: Orders/UpdateStatus
        [HttpPost]
        public async Task<IActionResult> UpdateStatus(int orderId, int orderStatusId)
        {
            try
            {
                var order = await _context.Orders.FindAsync(orderId);
                if (order != null)
                {
                    order.OrderStatusId = orderStatusId;
                    _context.Update(order);
                    await _context.SaveChangesAsync();
                    
                    TempData["SuccessMessage"] = "Trạng thái đơn hàng đã được cập nhật thành công!";
                }
                else
                {
                    TempData["ErrorMessage"] = "Không tìm thấy đơn hàng!";
                }
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Có lỗi xảy ra: " + ex.Message;
            }
            
            return RedirectToAction("Details", new { id = orderId });
        }

        // GET: Orders/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var order = await _context.Orders
                .Include(o => o.OrderDetails)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (order == null)
            {
                return NotFound();
            }

            // Soft delete Order và tất cả OrderDetails liên quan
            order.IsActive = false;

            // Soft delete tất cả OrderDetails của Order này
            var deletedDetailsCount = 0;
            foreach (var orderDetail in order.OrderDetails.Where(od => od.IsActive))
            {
                orderDetail.IsActive = false;
                deletedDetailsCount++;
            }

            _context.Update(order);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = $"Đơn hàng #{order.Id.ToString("D6")} và {deletedDetailsCount} chi tiết đơn hàng đã được xóa thành công!";
            return RedirectToAction(nameof(Index));
        }

        // GET: Orders/Restore/5 - Khôi phục đơn hàng đã xóa
        public async Task<IActionResult> Restore(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var order = await _context.Orders
                .Include(o => o.OrderDetails)
                .FirstOrDefaultAsync(m => m.Id == id && !m.IsActive);

            if (order == null)
            {
                TempData["ErrorMessage"] = "Không tìm thấy đơn hàng đã xóa!";
                return RedirectToAction(nameof(Index));
            }

            // Restore Order và tất cả OrderDetails liên quan
            order.IsActive = true;

            // Restore tất cả OrderDetails của Order này
            var restoredDetailsCount = 0;
            foreach (var orderDetail in order.OrderDetails.Where(od => !od.IsActive))
            {
                orderDetail.IsActive = true;
                restoredDetailsCount++;
            }

            _context.Update(order);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = $"Đơn hàng #{order.Id.ToString("D6")} và {restoredDetailsCount} chi tiết đơn hàng đã được khôi phục thành công!";
            return RedirectToAction(nameof(Index));
        }

        private async Task PopulateDropDowns(object? selectedAccount = null, object? selectedOrderStatus = null)
        {
            var accounts = await _context.Accounts.Where(a => a.IsActive).OrderBy(a => a.FirstName).ToListAsync();
            var orderStatuses = await _context.OrderStatuses.Where(os => os.IsActive).OrderBy(os => os.Name).ToListAsync();

            // Create account dropdown with full name display
            var accountList = accounts.Select(a => new {
                Id = a.Id,
                DisplayName = $"{a.FirstName} {a.LastName} ({a.UserName})"
            }).ToList();

            ViewBag.AccountId = new SelectList(accountList, "Id", "DisplayName", selectedAccount);
            ViewBag.OrderStatusId = new SelectList(orderStatuses, "Id", "Name", selectedOrderStatus);
        }

        private async Task PopulateOrderStatusDropDown(object? selectedOrderStatus = null)
        {
            var orderStatuses = await _context.OrderStatuses.Where(os => os.IsActive).OrderBy(os => os.Name).ToListAsync();
            ViewBag.OrderStatusId = new SelectList(orderStatuses, "Id", "Name", selectedOrderStatus);
        }

        private async Task PopulateDropDownsForCreate()
        {
            var accounts = await _context.Accounts.Where(a => a.IsActive).OrderBy(a => a.FirstName).ToListAsync();
            var orderStatuses = await _context.OrderStatuses.Where(os => os.IsActive).OrderBy(os => os.Name).ToListAsync();
            var products = await _context.Products.Where(p => p.IsActive).OrderBy(p => p.Name).ToListAsync();

            // Create account dropdown with full name display
            var accountList = accounts.Select(a => new {
                Id = a.Id,
                DisplayName = $"{a.FirstName} {a.LastName} ({a.UserName})"
            }).ToList();

            // Create product dropdown with price display
            var productList = products.Select(p => new {
                Id = p.Id,
                DisplayName = $"{p.Name} - {p.Price:C0}",
                Price = p.Price
            }).ToList();

            ViewBag.AccountId = new SelectList(accountList, "Id", "DisplayName");
            ViewBag.OrderStatusId = new SelectList(orderStatuses, "Id", "Name");
            ViewBag.ProductId = new SelectList(productList, "Id", "DisplayName");
            ViewBag.Products = products.ToDictionary(p => p.Id, p => new { p.Name, p.Price });
        }

        private bool OrderExists(int id)
        {
            return _context.Orders.Any(e => e.Id == id);
        }
    }
}
