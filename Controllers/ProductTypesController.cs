using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using HSU_NguyenHoangThanhSang_22207613.Data;
using HSU_NguyenHoangThanhSang_22207613.Models;

namespace HSU_NguyenHoangThanhSang_22207613.Controllers
{
    public class ProductTypesController : BaseController
    {
        private readonly ApplicationDbContext _context;

        public ProductTypesController(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            var productTypes = await _context.ProductTypes.Where(pt => pt.IsActive).OrderBy(pt => pt.Name).ToListAsync();
            return View(productTypes);
        }

        public async Task<IActionResult> Details(int? id)
        {
            if (id == null) return NotFound();
            var productType = await _context.ProductTypes
                .Include(pt => pt.Products.Where(p => p.IsActive))
                    .ThenInclude(p => p.Category)
                .Include(pt => pt.Products)
                    .ThenInclude(p => p.Supplier)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (productType == null) return NotFound();
            return View(productType);
        }

        public IActionResult CreateOrEdit(int? id)
        {
            if (id == null) return View(new ProductType());
            var productType = _context.ProductTypes.Find(id);
            if (productType == null) return NotFound();
            return View(productType);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateOrEdit(ProductType productType)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    var existing = await _context.ProductTypes.Where(pt => pt.Id != productType.Id && pt.Code == productType.Code).FirstOrDefaultAsync();
                    if (existing != null)
                    {
                        ModelState.AddModelError("Code", "Mã loại sản phẩm đã tồn tại.");
                        return View(productType);
                    }

                    if (productType.Id == 0)
                    {
                        productType.CreatedDate = DateTime.Now;
                        _context.Add(productType);
                        TempData["SuccessMessage"] = "Loại sản phẩm đã được tạo thành công!";
                    }
                    else
                    {
                        _context.Update(productType);
                        TempData["SuccessMessage"] = "Loại sản phẩm đã được cập nhật thành công!";
                    }
                    await _context.SaveChangesAsync();
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = "Có lỗi xảy ra: " + ex.Message;
                }
            }
            return View(productType);
        }

        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null) return NotFound();
            var productType = await _context.ProductTypes.FirstOrDefaultAsync(m => m.Id == id);
            if (productType == null) return NotFound();

            var hasProducts = await _context.Products.AnyAsync(p => p.ProductTypeId == id);
            if (hasProducts)
            {
                TempData["ErrorMessage"] = "Không thể xóa loại sản phẩm này vì đang có sản phẩm liên quan!";
                return RedirectToAction(nameof(Index));
            }

            productType.IsActive = false;
            _context.Update(productType);
            await _context.SaveChangesAsync();
            TempData["SuccessMessage"] = "Loại sản phẩm đã được xóa thành công!";
            return RedirectToAction(nameof(Index));
        }
    }
}
