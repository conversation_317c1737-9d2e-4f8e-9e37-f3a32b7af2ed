using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using HSU_NguyenHoangThanhSang_22207613.Data;
using HSU_NguyenHoangThanhSang_22207613.Models;

namespace HSU_NguyenHoangThanhSang_22207613.Controllers
{
    public class ProductsController : BaseController
    {
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _webHostEnvironment;

        public ProductsController(ApplicationDbContext context, IWebHostEnvironment webHostEnvironment)
        {
            _context = context;
            _webHostEnvironment = webHostEnvironment;
        }

        // GET: Products
        public async Task<IActionResult> Index()
        {
            var products = await _context.Products
                .Include(p => p.Category)
                .Include(p => p.Supplier)
                .Include(p => p.ProductType)
                .Where(p => p.IsActive)
                .OrderBy(p => p.Name)
                .ToListAsync();
            return View(products);
        }

        // GET: Products/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var product = await _context.Products
                .Include(p => p.Category)
                .Include(p => p.Supplier)
                .Include(p => p.ProductType)
                .Include(p => p.OrderDetails)
                    .ThenInclude(od => od.Order)
                    .ThenInclude(o => o.Account)
                .FirstOrDefaultAsync(m => m.Id == id);
            
            if (product == null)
            {
                return NotFound();
            }

            return View(product);
        }

        // GET: Products/CreateOrEdit
        public async Task<IActionResult> CreateOrEdit(int? id)
        {
            await PopulateDropDowns();
            
            if (id == null)
            {
                // Create new
                return View(new Product());
            }
            else
            {
                // Edit existing
                var product = await _context.Products.FindAsync(id);
                if (product == null)
                {
                    return NotFound();
                }
                return View(product);
            }
        }

        // POST: Products/CreateOrEdit
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateOrEdit(Product product, IFormFile? thumbnailFile)
        {
            // Remove navigation property validation
            ModelState.Remove("Category");
            ModelState.Remove("Supplier");
            ModelState.Remove("ProductType");
            ModelState.Remove("OrderDetails");

            if (ModelState.IsValid)
            {
                try
                {
                    // Handle file upload
                    if (thumbnailFile != null && thumbnailFile.Length > 0)
                    {
                        var uploadsFolder = Path.Combine(_webHostEnvironment.WebRootPath, "uploads", "products");
                        Directory.CreateDirectory(uploadsFolder);
                        
                        var uniqueFileName = Guid.NewGuid().ToString() + "_" + thumbnailFile.FileName;
                        var filePath = Path.Combine(uploadsFolder, uniqueFileName);
                        
                        using (var fileStream = new FileStream(filePath, FileMode.Create))
                        {
                            await thumbnailFile.CopyToAsync(fileStream);
                        }
                        
                        product.Thumbnail = "/uploads/products/" + uniqueFileName;
                    }

                    if (product.Id == 0)
                    {
                        // Create new
                        product.CreatedDate = DateTime.Now;
                        _context.Add(product);
                        TempData["SuccessMessage"] = "Sản phẩm đã được tạo thành công!";
                    }
                    else
                    {
                        // Update existing
                        _context.Update(product);
                        TempData["SuccessMessage"] = "Sản phẩm đã được cập nhật thành công!";
                    }
                    
                    await _context.SaveChangesAsync();
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = "Có lỗi xảy ra: " + ex.Message;
                }
            }
            
            await PopulateDropDowns(product.CategoryId, product.SupplierId, product.ProductTypeId);
            return View(product);
        }

        // GET: Products/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var product = await _context.Products
                .FirstOrDefaultAsync(m => m.Id == id);
            
            if (product == null)
            {
                return NotFound();
            }

            // Check if product has any order details
            var hasOrderDetails = await _context.OrderDetails.AnyAsync(od => od.ProductId == id);
            if (hasOrderDetails)
            {
                TempData["ErrorMessage"] = "Không thể xóa sản phẩm này vì đang có đơn hàng liên quan!";
                return RedirectToAction(nameof(Index));
            }

            // Soft delete
            product.IsActive = false;
            _context.Update(product);
            await _context.SaveChangesAsync();
            
            TempData["SuccessMessage"] = "Sản phẩm đã được xóa thành công!";
            return RedirectToAction(nameof(Index));
        }

        private async Task PopulateDropDowns(object? selectedCategory = null, object? selectedSupplier = null, object? selectedProductType = null)
        {
            var categories = await _context.Categories.Where(c => c.IsActive).OrderBy(c => c.Name).ToListAsync();
            var suppliers = await _context.Suppliers.Where(s => s.IsActive).OrderBy(s => s.Name).ToListAsync();
            var productTypes = await _context.ProductTypes.Where(pt => pt.IsActive).OrderBy(pt => pt.Name).ToListAsync();
            
            ViewBag.CategoryId = new SelectList(categories, "Id", "Name", selectedCategory);
            ViewBag.SupplierId = new SelectList(suppliers, "Id", "Name", selectedSupplier);
            ViewBag.ProductTypeId = new SelectList(productTypes, "Id", "Name", selectedProductType);
        }

        private bool ProductExists(int id)
        {
            return _context.Products.Any(e => e.Id == id);
        }
    }
}
