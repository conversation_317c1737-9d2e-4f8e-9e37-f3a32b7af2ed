using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using HSU_NguyenHoangThanhSang_22207613.Data;
using HSU_NguyenHoangThanhSang_22207613.Models;

namespace HSU_NguyenHoangThanhSang_22207613.Controllers
{
    public class SuppliersController : BaseController
    {
        private readonly ApplicationDbContext _context;

        public SuppliersController(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            var suppliers = await _context.Suppliers.Where(s => s.IsActive).OrderBy(s => s.Name).ToListAsync();
            return View(suppliers);
        }

        public async Task<IActionResult> Details(int? id)
        {
            if (id == null) return NotFound();
            var supplier = await _context.Suppliers
                .Include(s => s.Products.Where(p => p.IsActive))
                    .ThenInclude(p => p.Category)
                .Include(s => s.Products)
                    .ThenInclude(p => p.ProductType)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (supplier == null) return NotFound();
            return View(supplier);
        }

        public IActionResult CreateOrEdit(int? id)
        {
            if (id == null) return View(new Supplier());
            var supplier = _context.Suppliers.Find(id);
            if (supplier == null) return NotFound();
            return View(supplier);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateOrEdit(Supplier supplier)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    var existing = await _context.Suppliers.Where(s => s.Id != supplier.Id && s.Code == supplier.Code).FirstOrDefaultAsync();
                    if (existing != null)
                    {
                        ModelState.AddModelError("Code", "Mã nhà cung cấp đã tồn tại.");
                        return View(supplier);
                    }

                    if (supplier.Id == 0)
                    {
                        supplier.CreatedDate = DateTime.Now;
                        _context.Add(supplier);
                        TempData["SuccessMessage"] = "Nhà cung cấp đã được tạo thành công!";
                    }
                    else
                    {
                        _context.Update(supplier);
                        TempData["SuccessMessage"] = "Nhà cung cấp đã được cập nhật thành công!";
                    }
                    await _context.SaveChangesAsync();
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = "Có lỗi xảy ra: " + ex.Message;
                }
            }
            return View(supplier);
        }

        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null) return NotFound();
            var supplier = await _context.Suppliers.FirstOrDefaultAsync(m => m.Id == id);
            if (supplier == null) return NotFound();

            var hasProducts = await _context.Products.AnyAsync(p => p.SupplierId == id);
            if (hasProducts)
            {
                TempData["ErrorMessage"] = "Không thể xóa nhà cung cấp này vì đang có sản phẩm liên quan!";
                return RedirectToAction(nameof(Index));
            }

            supplier.IsActive = false;
            _context.Update(supplier);
            await _context.SaveChangesAsync();
            TempData["SuccessMessage"] = "Nhà cung cấp đã được xóa thành công!";
            return RedirectToAction(nameof(Index));
        }
    }
}
