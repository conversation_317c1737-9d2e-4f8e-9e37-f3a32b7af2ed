using Microsoft.EntityFrameworkCore;
using HSU_NguyenHoangThanhSang_22207613.Models;

namespace HSU_NguyenHoangThanhSang_22207613.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<Group> Groups { get; set; }
        public DbSet<Account> Accounts { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<Article> Articles { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<ProductType> ProductTypes { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<OrderStatus> OrderStatuses { get; set; }
        public DbSet<Order> Orders { get; set; }
        public DbSet<OrderDetail> OrderDetails { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure relationships and constraints
            modelBuilder.Entity<Account>()
                .HasOne(a => a.Group)
                .WithMany(g => g.Accounts)
                .HasForeignKey(a => a.GroupId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Article>()
                .HasOne(a => a.Category)
                .WithMany(c => c.Articles)
                .HasForeignKey(a => a.CategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Product>()
                .HasOne(p => p.Category)
                .WithMany(c => c.Products)
                .HasForeignKey(p => p.CategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Product>()
                .HasOne(p => p.Supplier)
                .WithMany(s => s.Products)
                .HasForeignKey(p => p.SupplierId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Product>()
                .HasOne(p => p.ProductType)
                .WithMany(pt => pt.Products)
                .HasForeignKey(p => p.ProductTypeId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Order>()
                .HasOne(o => o.Account)
                .WithMany(a => a.Orders)
                .HasForeignKey(o => o.AccountId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Order>()
                .HasOne(o => o.OrderStatus)
                .WithMany(os => os.Orders)
                .HasForeignKey(o => o.OrderStatusId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<OrderDetail>()
                .HasOne(od => od.Order)
                .WithMany(o => o.OrderDetails)
                .HasForeignKey(od => od.OrderId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<OrderDetail>()
                .HasOne(od => od.Product)
                .WithMany(p => p.OrderDetails)
                .HasForeignKey(od => od.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            // Seed data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            var seedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);

            // Seed Groups
            modelBuilder.Entity<Group>().HasData(
                new Group { Id = 1, Code = "ADMIN", Name = "Administrator", IsActive = true, CreatedDate = seedDate },
                new Group { Id = 2, Code = "USER", Name = "User", IsActive = true, CreatedDate = seedDate }
            );

            // Seed OrderStatus
            modelBuilder.Entity<OrderStatus>().HasData(
                new OrderStatus { Id = 1, Code = "PENDING", Name = "Pending", IsActive = true, CreatedDate = seedDate },
                new OrderStatus { Id = 2, Code = "PROCESSING", Name = "Processing", IsActive = true, CreatedDate = seedDate },
                new OrderStatus { Id = 3, Code = "SHIPPED", Name = "Shipped", IsActive = true, CreatedDate = seedDate },
                new OrderStatus { Id = 4, Code = "DELIVERED", Name = "Delivered", IsActive = true, CreatedDate = seedDate },
                new OrderStatus { Id = 5, Code = "CANCELLED", Name = "Cancelled", IsActive = true, CreatedDate = seedDate }
            );

            // Seed Categories
            modelBuilder.Entity<Category>().HasData(
                new Category { Id = 1, Code = "TECH", Name = "Technology", IsActive = true, CreatedDate = seedDate },
                new Category { Id = 2, Code = "FASHION", Name = "Fashion", IsActive = true, CreatedDate = seedDate }
            );

            // Seed Admin Account
            modelBuilder.Entity<Account>().HasData(
                new Account
                {
                    Id = 1,
                    FirstName = "Admin",
                    LastName = "User",
                    Email = "<EMAIL>",
                    UserName = "admin",
                    Password = "admin123", // In production, this should be hashed
                    IsActive = true,
                    CreatedDate = seedDate,
                    GroupId = 1
                }
            );
        }
    }
}
