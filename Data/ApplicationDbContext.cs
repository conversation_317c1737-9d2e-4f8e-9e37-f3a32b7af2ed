using Microsoft.EntityFrameworkCore;
using HSU_NguyenHoangThanhSang_22207613.Models;

namespace HSU_NguyenHoangThanhSang_22207613.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<Group> Groups { get; set; }
        public DbSet<Account> Accounts { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<Article> Articles { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<ProductType> ProductTypes { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<OrderStatus> OrderStatuses { get; set; }
        public DbSet<Order> Orders { get; set; }
        public DbSet<OrderDetail> OrderDetails { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure relationships and constraints
            modelBuilder.Entity<Account>()
                .HasOne(a => a.Group)
                .WithMany(g => g.Accounts)
                .HasForeignKey(a => a.GroupId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Article>()
                .HasOne(a => a.Category)
                .WithMany(c => c.Articles)
                .HasForeignKey(a => a.CategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Product>()
                .HasOne(p => p.Category)
                .WithMany(c => c.Products)
                .HasForeignKey(p => p.CategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Product>()
                .HasOne(p => p.Supplier)
                .WithMany(s => s.Products)
                .HasForeignKey(p => p.SupplierId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Product>()
                .HasOne(p => p.ProductType)
                .WithMany(pt => pt.Products)
                .HasForeignKey(p => p.ProductTypeId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Order>()
                .HasOne(o => o.Account)
                .WithMany(a => a.Orders)
                .HasForeignKey(o => o.AccountId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Order>()
                .HasOne(o => o.OrderStatus)
                .WithMany(os => os.Orders)
                .HasForeignKey(o => o.OrderStatusId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<OrderDetail>()
                .HasOne(od => od.Order)
                .WithMany(o => o.OrderDetails)
                .HasForeignKey(od => od.OrderId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<OrderDetail>()
                .HasOne(od => od.Product)
                .WithMany(p => p.OrderDetails)
                .HasForeignKey(od => od.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            // Seed data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            var seedDate = new DateTime(2025, 7, 20, 0, 0, 0, DateTimeKind.Utc);

            // Seed Groups
            modelBuilder.Entity<Group>().HasData(
                new Group { Id = 1, Code = "ADMIN", Name = "Quản trị viên", IsActive = true, CreatedDate = seedDate },
                new Group { Id = 2, Code = "USER", Name = "Người dùng", IsActive = true, CreatedDate = seedDate }
            );

            // Seed Admin Account
            modelBuilder.Entity<Account>().HasData(
                new Account
                {
                    Id = 1,
                    FirstName = "Sang",
                    LastName = "Nguyen",
                    Email = "<EMAIL>",
                    UserName = "sangnht",
                    Password = "Admin@1234",
                    IsActive = true,
                    CreatedDate = seedDate,
                    GroupId = 1
                }
            );

            // Seed OrderStatus
            modelBuilder.Entity<OrderStatus>().HasData(
                new OrderStatus { Id = 1, Code = "NEW", Name = "Mới", IsActive = true, CreatedDate = seedDate },
                new OrderStatus { Id = 2, Code = "PROCESSING", Name = "Đang xử lý", IsActive = true, CreatedDate = seedDate },
                new OrderStatus { Id = 3, Code = "PENDING", Name = "Chờ xử lý", IsActive = true, CreatedDate = seedDate },
                new OrderStatus { Id = 4, Code = "PAID", Name = "Đã thanh toán", IsActive = true, CreatedDate = seedDate },
                new OrderStatus { Id = 5, Code = "CANCELLED", Name = "Đã huỷ", IsActive = true, CreatedDate = seedDate },
                new OrderStatus { Id = 6, Code = "REFUNDED", Name = "Đã hoàn tiền", IsActive = true, CreatedDate = seedDate },
                new OrderStatus { Id = 7, Code = "DELIVERED", Name = "Đã giao hàng", IsActive = true, CreatedDate = seedDate },
                new OrderStatus { Id = 8, Code = "COMPLETED", Name = "Hoàn thành", IsActive = true, CreatedDate = seedDate }
            );

            // Seed Categories
            modelBuilder.Entity<Category>().HasData(
                new Category { Id = 1, Code = "HDMS", Name = "Hướng dẫn mua sắm", IsActive = true, CreatedDate = seedDate },
                new Category { Id = 2, Code = "TNKM", Name = "Tin tức & Khuyến mãi", IsActive = true, CreatedDate = seedDate },
                new Category { Id = 3, Code = "MVCS", Name = "Mẹo vặt & Cuộc sống", IsActive = true, CreatedDate = seedDate },
                new Category { Id = 4, Code = "CSHT", Name = "Chính sách & Hỗ trợ", IsActive = true, CreatedDate = seedDate }
            );

            // Seed Suppliers
            modelBuilder.Entity<Supplier>().HasData(
                new Supplier { Id = 1, Code = "MWG001", Name = "Công ty Cổ phần Thế Giới Di Động", IsActive = true, CreatedDate = seedDate },
                new Supplier { Id = 2, Code = "VNMILK002", Name = "Công ty Cổ phần Sữa Việt Nam", IsActive = true, CreatedDate = seedDate },
                new Supplier { Id = 3, Code = "VINGR003", Name = "Tập đoàn Vingroup", IsActive = true, CreatedDate = seedDate },
                new Supplier { Id = 4, Code = "FPTRE004", Name = "Công ty Cổ phần FPT Retail", IsActive = true, CreatedDate = seedDate },
                new Supplier { Id = 5, Code = "HPG005", Name = "Tập đoàn Hòa Phát", IsActive = true, CreatedDate = seedDate }
            );

            // Seed ProductType
            modelBuilder.Entity<ProductType >().HasData(
                new ProductType { Id = 1, Code = "DM_DTDD", Name = "Điện thoại di động", IsActive = true, CreatedDate = seedDate },
                new ProductType { Id = 2, Code = "DM_MTB", Name = "Máy tính bảng", IsActive = true, CreatedDate = seedDate },
                new ProductType { Id = 3, Code = "DM_LAPT", Name = "Máy tính xách tay", IsActive = true, CreatedDate = seedDate },
                new ProductType { Id = 4, Code = "DM_DHTM", Name = "Đồng hồ thông minh", IsActive = true, CreatedDate = seedDate },
                new ProductType { Id = 5, Code = "DM_SNUOC", Name = "Sữa nước", IsActive = true, CreatedDate = seedDate },
                new ProductType { Id = 6, Code = "DM_SBOT", Name = "Sữa bột", IsActive = true, CreatedDate = seedDate },
                new ProductType { Id = 7, Code = "DM_SCHUA", Name = "Sữa chua", IsActive = true, CreatedDate = seedDate },
                new ProductType { Id = 8, Code = "DM_TPTS", Name = "Thực phẩm tươi sống", IsActive = true, CreatedDate = seedDate },
                new ProductType { Id = 9, Code = "DM_TPKD", Name = "Thực phẩm khô & Đóng gói", IsActive = true, CreatedDate = seedDate },
                new ProductType { Id = 10, Code = "DM_DDGD", Name = "Đồ dùng gia đình", IsActive = true, CreatedDate = seedDate }
            );
        }
    }
}
