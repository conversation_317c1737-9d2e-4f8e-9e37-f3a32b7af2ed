﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace HSU_NguyenHoangThanhSang_22207613.Migrations
{
    /// <inheritdoc />
    public partial class Init_Database : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Categories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Code = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Categories", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Groups",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Code = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Groups", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "OrderStatuses",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Code = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OrderStatuses", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ProductTypes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Code = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProductTypes", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Suppliers",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Code = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Suppliers", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Articles",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Code = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Title = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    Content = table.Column<string>(type: "TEXT", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CategoryId = table.Column<int>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Articles", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Articles_Categories_CategoryId",
                        column: x => x.CategoryId,
                        principalTable: "Categories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Accounts",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    FirstName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    LastName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Email = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    UserName = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Password = table.Column<string>(type: "TEXT", maxLength: 255, nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    GroupId = table.Column<int>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Accounts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Accounts_Groups_GroupId",
                        column: x => x.GroupId,
                        principalTable: "Groups",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Products",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Thumbnail = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    Name = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    Price = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CategoryId = table.Column<int>(type: "INTEGER", nullable: false),
                    SupplierId = table.Column<int>(type: "INTEGER", nullable: true),
                    ProductTypeId = table.Column<int>(type: "INTEGER", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Products", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Products_Categories_CategoryId",
                        column: x => x.CategoryId,
                        principalTable: "Categories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Products_ProductTypes_ProductTypeId",
                        column: x => x.ProductTypeId,
                        principalTable: "ProductTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_Products_Suppliers_SupplierId",
                        column: x => x.SupplierId,
                        principalTable: "Suppliers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "Orders",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    OrderDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    AccountId = table.Column<int>(type: "INTEGER", nullable: false),
                    OrderStatusId = table.Column<int>(type: "INTEGER", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Orders", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Orders_Accounts_AccountId",
                        column: x => x.AccountId,
                        principalTable: "Accounts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Orders_OrderStatuses_OrderStatusId",
                        column: x => x.OrderStatusId,
                        principalTable: "OrderStatuses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "OrderDetails",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    OrderId = table.Column<int>(type: "INTEGER", nullable: false),
                    ProductId = table.Column<int>(type: "INTEGER", nullable: false),
                    Quantity = table.Column<int>(type: "INTEGER", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OrderDetails", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OrderDetails_Orders_OrderId",
                        column: x => x.OrderId,
                        principalTable: "Orders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_OrderDetails_Products_ProductId",
                        column: x => x.ProductId,
                        principalTable: "Products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "Categories",
                columns: new[] { "Id", "Code", "CreatedDate", "IsActive", "Name" },
                values: new object[,]
                {
                    { 1, "HDMS", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Hướng dẫn mua sắm" },
                    { 2, "TNKM", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Tin tức & Khuyến mãi" },
                    { 3, "MVCS", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Mẹo vặt & Cuộc sống" },
                    { 4, "CSHT", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Chính sách & Hỗ trợ" }
                });

            migrationBuilder.InsertData(
                table: "Groups",
                columns: new[] { "Id", "Code", "CreatedDate", "IsActive", "Name" },
                values: new object[,]
                {
                    { 1, "ADMIN", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Quản trị viên" },
                    { 2, "USER", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Người dùng" }
                });

            migrationBuilder.InsertData(
                table: "OrderStatuses",
                columns: new[] { "Id", "Code", "CreatedDate", "IsActive", "Name" },
                values: new object[,]
                {
                    { 1, "NEW", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Mới" },
                    { 2, "PROCESSING", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Đang xử lý" },
                    { 3, "PENDING", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Chờ xử lý" },
                    { 4, "PAID", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Đã thanh toán" },
                    { 5, "CANCELLED", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Đã huỷ" },
                    { 6, "REFUNDED", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Đã hoàn tiền" },
                    { 7, "DELIVERED", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Đã giao hàng" },
                    { 8, "COMPLETED", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Hoàn thành" }
                });

            migrationBuilder.InsertData(
                table: "ProductTypes",
                columns: new[] { "Id", "Code", "CreatedDate", "IsActive", "Name" },
                values: new object[,]
                {
                    { 1, "DM_DTDD", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Điện thoại di động" },
                    { 2, "DM_MTB", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Máy tính bảng" },
                    { 3, "DM_LAPT", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Máy tính xách tay" },
                    { 4, "DM_DHTM", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Đồng hồ thông minh" },
                    { 5, "DM_SNUOC", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Sữa nước" },
                    { 6, "DM_SBOT", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Sữa bột" },
                    { 7, "DM_SCHUA", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Sữa chua" },
                    { 8, "DM_TPTS", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Thực phẩm tươi sống" },
                    { 9, "DM_TPKD", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Thực phẩm khô & Đóng gói" },
                    { 10, "DM_DDGD", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Đồ dùng gia đình" }
                });

            migrationBuilder.InsertData(
                table: "Suppliers",
                columns: new[] { "Id", "Code", "CreatedDate", "IsActive", "Name" },
                values: new object[,]
                {
                    { 1, "MWG001", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Công ty Cổ phần Thế Giới Di Động" },
                    { 2, "VNMILK002", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Công ty Cổ phần Sữa Việt Nam" },
                    { 3, "VINGR003", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Tập đoàn Vingroup" },
                    { 4, "FPTRE004", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Công ty Cổ phần FPT Retail" },
                    { 5, "HPG005", new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), true, "Tập đoàn Hòa Phát" }
                });

            migrationBuilder.InsertData(
                table: "Accounts",
                columns: new[] { "Id", "CreatedDate", "Email", "FirstName", "GroupId", "IsActive", "LastName", "Password", "UserName" },
                values: new object[] { 1, new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc), "<EMAIL>", "Sang", 1, true, "Nguyen", "Admin@1234", "sangnht" });

            migrationBuilder.CreateIndex(
                name: "IX_Accounts_GroupId",
                table: "Accounts",
                column: "GroupId");

            migrationBuilder.CreateIndex(
                name: "IX_Articles_CategoryId",
                table: "Articles",
                column: "CategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_OrderDetails_OrderId",
                table: "OrderDetails",
                column: "OrderId");

            migrationBuilder.CreateIndex(
                name: "IX_OrderDetails_ProductId",
                table: "OrderDetails",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_AccountId",
                table: "Orders",
                column: "AccountId");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_OrderStatusId",
                table: "Orders",
                column: "OrderStatusId");

            migrationBuilder.CreateIndex(
                name: "IX_Products_CategoryId",
                table: "Products",
                column: "CategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_Products_ProductTypeId",
                table: "Products",
                column: "ProductTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_Products_SupplierId",
                table: "Products",
                column: "SupplierId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Articles");

            migrationBuilder.DropTable(
                name: "OrderDetails");

            migrationBuilder.DropTable(
                name: "Orders");

            migrationBuilder.DropTable(
                name: "Products");

            migrationBuilder.DropTable(
                name: "Accounts");

            migrationBuilder.DropTable(
                name: "OrderStatuses");

            migrationBuilder.DropTable(
                name: "Categories");

            migrationBuilder.DropTable(
                name: "ProductTypes");

            migrationBuilder.DropTable(
                name: "Suppliers");

            migrationBuilder.DropTable(
                name: "Groups");
        }
    }
}
