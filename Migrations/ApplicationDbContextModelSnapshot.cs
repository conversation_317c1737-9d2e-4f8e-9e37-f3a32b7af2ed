﻿// <auto-generated />
using System;
using HSU_NguyenHoangThanhSang_22207613.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace HSU_NguyenHoangThanhSang_22207613.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.7");

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.Account", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int>("GroupId")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("GroupId");

                    b.ToTable("Accounts");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            Email = "<EMAIL>",
                            FirstName = "Sang",
                            GroupId = 1,
                            IsActive = true,
                            LastName = "Nguyen",
                            Password = "Admin@1234",
                            UserName = "sangnht"
                        });
                });

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.Article", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("CategoryId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.ToTable("Articles");
                });

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.Category", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Categories");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Code = "HDMS",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Hướng dẫn mua sắm"
                        },
                        new
                        {
                            Id = 2,
                            Code = "TNKM",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Tin tức & Khuyến mãi"
                        },
                        new
                        {
                            Id = 3,
                            Code = "MVCS",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Mẹo vặt & Cuộc sống"
                        },
                        new
                        {
                            Id = 4,
                            Code = "CSHT",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Chính sách & Hỗ trợ"
                        });
                });

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.Group", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Groups");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Code = "ADMIN",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Quản trị viên"
                        },
                        new
                        {
                            Id = 2,
                            Code = "USER",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Người dùng"
                        });
                });

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.Order", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("AccountId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("OrderDate")
                        .HasColumnType("TEXT");

                    b.Property<int?>("OrderStatusId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("AccountId");

                    b.HasIndex("OrderStatusId");

                    b.ToTable("Orders");
                });

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.OrderDetail", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<int>("OrderId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.HasIndex("ProductId");

                    b.ToTable("OrderDetails");
                });

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.OrderStatus", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("OrderStatuses");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Code = "NEW",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Mới"
                        },
                        new
                        {
                            Id = 2,
                            Code = "PROCESSING",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Đang xử lý"
                        },
                        new
                        {
                            Id = 3,
                            Code = "PENDING",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Chờ xử lý"
                        },
                        new
                        {
                            Id = 4,
                            Code = "PAID",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Đã thanh toán"
                        },
                        new
                        {
                            Id = 5,
                            Code = "CANCELLED",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Đã huỷ"
                        },
                        new
                        {
                            Id = 6,
                            Code = "REFUNDED",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Đã hoàn tiền"
                        },
                        new
                        {
                            Id = 7,
                            Code = "DELIVERED",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Đã giao hàng"
                        },
                        new
                        {
                            Id = 8,
                            Code = "COMPLETED",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Hoàn thành"
                        });
                });

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.Product", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("CategoryId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("ProductTypeId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("SupplierId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Thumbnail")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("ProductTypeId");

                    b.HasIndex("SupplierId");

                    b.ToTable("Products");
                });

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.ProductType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("ProductTypes");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Code = "DM_DTDD",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Điện thoại di động"
                        },
                        new
                        {
                            Id = 2,
                            Code = "DM_MTB",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Máy tính bảng"
                        },
                        new
                        {
                            Id = 3,
                            Code = "DM_LAPT",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Máy tính xách tay"
                        },
                        new
                        {
                            Id = 4,
                            Code = "DM_DHTM",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Đồng hồ thông minh"
                        },
                        new
                        {
                            Id = 5,
                            Code = "DM_SNUOC",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Sữa nước"
                        },
                        new
                        {
                            Id = 6,
                            Code = "DM_SBOT",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Sữa bột"
                        },
                        new
                        {
                            Id = 7,
                            Code = "DM_SCHUA",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Sữa chua"
                        },
                        new
                        {
                            Id = 8,
                            Code = "DM_TPTS",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Thực phẩm tươi sống"
                        },
                        new
                        {
                            Id = 9,
                            Code = "DM_TPKD",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Thực phẩm khô & Đóng gói"
                        },
                        new
                        {
                            Id = 10,
                            Code = "DM_DDGD",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Đồ dùng gia đình"
                        });
                });

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.Supplier", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Suppliers");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Code = "MWG001",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Công ty Cổ phần Thế Giới Di Động"
                        },
                        new
                        {
                            Id = 2,
                            Code = "VNMILK002",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Công ty Cổ phần Sữa Việt Nam"
                        },
                        new
                        {
                            Id = 3,
                            Code = "VINGR003",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Tập đoàn Vingroup"
                        },
                        new
                        {
                            Id = 4,
                            Code = "FPTRE004",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Công ty Cổ phần FPT Retail"
                        },
                        new
                        {
                            Id = 5,
                            Code = "HPG005",
                            CreatedDate = new DateTime(2025, 7, 20, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Name = "Tập đoàn Hòa Phát"
                        });
                });

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.Account", b =>
                {
                    b.HasOne("HSU_NguyenHoangThanhSang_22207613.Models.Group", "Group")
                        .WithMany("Accounts")
                        .HasForeignKey("GroupId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Group");
                });

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.Article", b =>
                {
                    b.HasOne("HSU_NguyenHoangThanhSang_22207613.Models.Category", "Category")
                        .WithMany("Articles")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Category");
                });

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.Order", b =>
                {
                    b.HasOne("HSU_NguyenHoangThanhSang_22207613.Models.Account", "Account")
                        .WithMany("Orders")
                        .HasForeignKey("AccountId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HSU_NguyenHoangThanhSang_22207613.Models.OrderStatus", "OrderStatus")
                        .WithMany("Orders")
                        .HasForeignKey("OrderStatusId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Account");

                    b.Navigation("OrderStatus");
                });

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.OrderDetail", b =>
                {
                    b.HasOne("HSU_NguyenHoangThanhSang_22207613.Models.Order", "Order")
                        .WithMany("OrderDetails")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("HSU_NguyenHoangThanhSang_22207613.Models.Product", "Product")
                        .WithMany("OrderDetails")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Order");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.Product", b =>
                {
                    b.HasOne("HSU_NguyenHoangThanhSang_22207613.Models.Category", "Category")
                        .WithMany("Products")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HSU_NguyenHoangThanhSang_22207613.Models.ProductType", "ProductType")
                        .WithMany("Products")
                        .HasForeignKey("ProductTypeId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("HSU_NguyenHoangThanhSang_22207613.Models.Supplier", "Supplier")
                        .WithMany("Products")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Category");

                    b.Navigation("ProductType");

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.Account", b =>
                {
                    b.Navigation("Orders");
                });

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.Category", b =>
                {
                    b.Navigation("Articles");

                    b.Navigation("Products");
                });

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.Group", b =>
                {
                    b.Navigation("Accounts");
                });

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.Order", b =>
                {
                    b.Navigation("OrderDetails");
                });

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.OrderStatus", b =>
                {
                    b.Navigation("Orders");
                });

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.Product", b =>
                {
                    b.Navigation("OrderDetails");
                });

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.ProductType", b =>
                {
                    b.Navigation("Products");
                });

            modelBuilder.Entity("HSU_NguyenHoangThanhSang_22207613.Models.Supplier", b =>
                {
                    b.Navigation("Products");
                });
#pragma warning restore 612, 618
        }
    }
}
