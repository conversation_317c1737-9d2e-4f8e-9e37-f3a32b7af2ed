using System.ComponentModel.DataAnnotations;

namespace HSU_NguyenHoangThanhSang_22207613.Models
{
    public class Category
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "Mã danh mục là bắt buộc")]
        [StringLength(50, ErrorMessage = "Mã danh mục không được vượt quá 50 ký tự")]
        public string Code { get; set; } = string.Empty;

        [Required(ErrorMessage = "Tên danh mục là bắt buộc")]
        [StringLength(200, ErrorMessage = "Tên danh mục không được vượt quá 200 ký tự")]
        public string Name { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<Article> Articles { get; set; } = new List<Article>();
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    }
}
