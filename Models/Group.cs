using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HSU_NguyenHoangThanhSang_22207613.Models
{
    public class Group
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "Mã nhóm là bắt buộc")]
        [StringLength(50, ErrorMessage = "Mã nhóm không được vượt quá 50 ký tự")]
        public string Code { get; set; } = string.Empty;

        [Required(ErrorMessage = "Tên nhóm là bắt buộc")]
        [StringLength(200, ErrorMessage = "Tên nhóm không được vượt quá 200 ký tự")]
        public string Name { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation property
        public virtual ICollection<Account> Accounts { get; set; } = new List<Account>();
    }
}
