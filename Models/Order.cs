using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace HSU_NguyenHoangThanhSang_22207613.Models
{
    public class Order
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "Ngày đặt hàng là bắt buộc")]
        public DateTime OrderDate { get; set; } = DateTime.Now;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Required(ErrorMessage = "<PERSON>h<PERSON>ch hàng là bắt buộc")]
        public int AccountId { get; set; }

        public int? OrderStatusId { get; set; }

        // Navigation properties
        [ForeignKey("AccountId")]
        [JsonIgnore]
        public virtual Account Account { get; set; } = null!;

        [ForeignKey("OrderStatusId")]
        [JsonIgnore]
        public virtual OrderStatus? OrderStatus { get; set; }

        [JsonIgnore]
        public virtual ICollection<OrderDetail> OrderDetails { get; set; } = new List<OrderDetail>();
    }
}
