using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace HSU_NguyenHoangThanhSang_22207613.Models
{
    public class Product
    {
        [Key]
        public int Id { get; set; }

        [StringLength(500, ErrorMessage = "Đường dẫn hình ảnh không được vượt quá 500 ký tự")]
        public string? Thumbnail { get; set; }

        [Required(ErrorMessage = "SKU là bắt buộc")]
        [StringLength(50, ErrorMessage = "SKU không được vượt quá 50 ký tự")]
        public string SKU { get; set; } = string.Empty;

        [Required(ErrorMessage = "Tên sản phẩm là bắt buộc")]
        [StringLength(200, ErrorMessage = "Tên sản phẩm không đượ<PERSON> vượt quá 200 ký tự")]
        public string Name { get; set; } = string.Empty;

        [StringLength(1000, ErrorMessage = "Mô tả sản phẩm không được vượ<PERSON> quá 1000 ký tự")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "Giá sản phẩm là bắt buộc")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Giá sản phẩm phải lớn hơn 0")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Required(ErrorMessage = "Danh mục sản phẩm là bắt buộc")]
        public int CategoryId { get; set; }

        public int? SupplierId { get; set; }

        public int? ProductTypeId { get; set; }

        // Navigation properties
        [ForeignKey("CategoryId")]
        [JsonIgnore]
        public virtual Category Category { get; set; } = null!;

        [ForeignKey("SupplierId")]
        [JsonIgnore]
        public virtual Supplier? Supplier { get; set; }

        [ForeignKey("ProductTypeId")]
        [JsonIgnore]
        public virtual ProductType? ProductType { get; set; }

        [JsonIgnore]
        public virtual ICollection<OrderDetail> OrderDetails { get; set; } = new List<OrderDetail>();
    }
}
