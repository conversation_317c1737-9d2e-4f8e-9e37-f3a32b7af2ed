using H<PERSON>_NguyenHoangThanhSang_22207613.Models;

namespace HSU_NguyenHoangThanhSang_22207613.Models.ViewModels
{
    public class DashboardViewModel
    {
        public int TotalOrders { get; set; }
        public int TotalProducts { get; set; }
        public int TotalCustomers { get; set; }
        public int TotalCategories { get; set; }
        public decimal TotalRevenue { get; set; }
        public int TodayOrders { get; set; }
        public List<TopSellingProductViewModel> TopSellingProducts { get; set; } = new();
        public List<Order> RecentOrders { get; set; } = new();
    }

    public class TopSellingProductViewModel
    {
        public string ProductName { get; set; } = string.Empty;
        public int TotalSold { get; set; }
    }
}
