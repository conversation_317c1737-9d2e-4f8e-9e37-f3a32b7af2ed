using System.ComponentModel.DataAnnotations;

namespace HSU_NguyenHoangThanhSang_22207613.Models.ViewModels
{
    public class OrderCreateViewModel
    {
        // Order properties
        [Required]
        public int AccountId { get; set; }
        
        [Required]
        public DateTime OrderDate { get; set; } = DateTime.Now;
        
        public int? OrderStatusId { get; set; }
        
        public bool IsActive { get; set; } = true;

        // Order details
        public List<OrderDetailItem> OrderDetails { get; set; } = new List<OrderDetailItem>();
    }

    public class OrderDetailItem
    {
        [Required]
        public int ProductId { get; set; }
        
        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "Số lượng phải lớn hơn 0")]
        public int Quantity { get; set; }
        
        // For display purposes
        public string? ProductName { get; set; }
        public decimal ProductPrice { get; set; }
        public decimal TotalPrice => ProductPrice * Quantity;
    }
}
