@model HSU_NguyenHoangThanhSang_22207613.Models.Account

@{
    ViewData["Title"] = Model.Id == 0 ? "Thêm mới Tà<PERSON> kho<PERSON>n" : "Chỉnh sửa Tà<PERSON> kho<PERSON>n";
    var isEdit = Model.Id != 0;
}

<div class="row">
    <div class="col-lg-10 col-md-12 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-@(isEdit ? "edit" : "plus") me-2"></i>@ViewData["Title"]
                </h5>
            </div>
            <div class="card-body">
                <form asp-action="CreateOrEdit" method="post">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    @if (isEdit)
                    {
                        <input type="hidden" asp-for="Id" />
                        <input type="hidden" asp-for="CreatedDate" />
                    }

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="FirstName" class="form-label">
                                    <i class="fas fa-user me-2"></i>Họ <span class="text-danger">*</span>
                                </label>
                                <input asp-for="FirstName" class="form-control" placeholder="Nhập họ" />
                                <span asp-validation-for="FirstName" class="text-danger"></span>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="LastName" class="form-label">
                                    <i class="fas fa-user me-2"></i>Tên <span class="text-danger">*</span>
                                </label>
                                <input asp-for="LastName" class="form-control" placeholder="Nhập tên" />
                                <span asp-validation-for="LastName" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="UserName" class="form-label">
                                    <i class="fas fa-at me-2"></i>Tên đăng nhập <span class="text-danger">*</span>
                                </label>
                                <input asp-for="UserName" class="form-control" placeholder="Nhập tên đăng nhập" />
                                <span asp-validation-for="UserName" class="text-danger"></span>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Email" class="form-label">
                                    <i class="fas fa-envelope me-2"></i>Email <span class="text-danger">*</span>
                                </label>
                                <input asp-for="Email" class="form-control" type="email" placeholder="Nhập email" />
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>Mật khẩu @if (!isEdit) { <span class="text-danger">*</span> }
                                </label>
                                @if (isEdit)
                                {
                                    <input asp-for="Password" class="form-control" type="password" placeholder="Để trống nếu không thay đổi" />
                                }
                                else
                                {
                                    <input asp-for="Password" class="form-control" type="password" placeholder="Nhập mật khẩu" required />
                                }
                                <span asp-validation-for="Password" class="text-danger"></span>
                                @if (isEdit)
                                {
                                    <small class="form-text text-muted">Để trống nếu không muốn thay đổi mật khẩu</small>
                                }
                                else
                                {
                                    <small class="form-text text-muted">Mật khẩu là bắt buộc khi tạo tài khoản mới</small>
                                }
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="GroupId" class="form-label">
                                    <i class="fas fa-users-cog me-2"></i>Nhóm quyền <span class="text-danger">*</span>
                                </label>
                                <select asp-for="GroupId" class="form-select" asp-items="ViewBag.GroupId">
                                    <option value="">-- Chọn nhóm quyền --</option>
                                </select>
                                <span asp-validation-for="GroupId" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input asp-for="IsActive" class="form-check-input" type="checkbox" role="switch" />
                            <label asp-for="IsActive" class="form-check-label">
                                Trạng thái hoạt động
                            </label>
                        </div>
                        <small class="form-text text-muted">Bật để kích hoạt tài khoản này</small>
                    </div>

                    @if (isEdit)
                    {
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-calendar me-2"></i>Ngày tạo
                            </label>
                            <input type="text" class="form-control" value="@Model.CreatedDate.ToString("dd/MM/yyyy HH:mm")" readonly />
                        </div>
                    }

                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Quay lại
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>@(isEdit ? "Cập nhật" : "Tạo mới")
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Auto-generate username from first and last name
        function generateUsername() {
            var firstName = document.getElementById('FirstName').value.toLowerCase();
            var lastName = document.getElementById('LastName').value.toLowerCase();
            if (firstName && lastName && document.getElementById('UserName').value === '') {
                var username = firstName.charAt(0) + lastName.replace(/\s+/g, '');
                document.getElementById('UserName').value = username;
            }
        }
        
        document.getElementById('FirstName').addEventListener('input', generateUsername);
        document.getElementById('LastName').addEventListener('input', generateUsername);
    </script>
}
