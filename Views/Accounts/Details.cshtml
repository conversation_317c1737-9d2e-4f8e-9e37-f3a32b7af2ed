@model HSU_NguyenHoangThanhSang_22207613.Models.Account

@{
    ViewData["Title"] = "Chi tiết Tài khoản";
}

<div class="row">
    <div class="col-lg-8 col-md-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Chi tiết Tài khoản
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-at me-2"></i>Tên đăng nhập
                            </label>
                            <div class="form-control-plaintext">
                                <span class="badge bg-info fs-6">@Model.UserName</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-users-cog me-2"></i>Nhóm quyền
                            </label>
                            <div class="form-control-plaintext">
                                <span class="badge bg-secondary fs-6">@Model.Group.Name</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-user me-2"></i>Họ
                            </label>
                            <div class="form-control-plaintext">@Model.FirstName</div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-user me-2"></i>Tên
                            </label>
                            <div class="form-control-plaintext">@Model.LastName</div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label fw-bold">
                        <i class="fas fa-envelope me-2"></i>Email
                    </label>
                    <div class="form-control-plaintext">
                        <a href="mailto:@Model.Email" class="text-decoration-none">@Model.Email</a>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-toggle-on me-2"></i>Trạng thái
                            </label>
                            <div class="form-control-plaintext">
                                @if (Model.IsActive)
                                {
                                    <span class="badge bg-success">Hoạt động</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">Không hoạt động</span>
                                }
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar me-2"></i>Ngày tạo
                            </label>
                            <div class="form-control-plaintext">@Model.CreatedDate.ToString("dd/MM/yyyy HH:mm")</div>
                        </div>
                    </div>
                </div>

                @if (Model.Orders != null && Model.Orders.Any())
                {
                    <div class="mb-3">
                        <label class="form-label fw-bold">
                            <i class="fas fa-shopping-cart me-2"></i>Đơn hàng (@Model.Orders.Count())
                        </label>
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead class="table-light">
                                    <tr>
                                        <th>Mã đơn hàng</th>
                                        <th>Ngày đặt</th>
                                        <th>Trạng thái</th>
                                        <th>Số sản phẩm</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var order in Model.Orders.Where(o => o.IsActive).Take(5))
                                    {
                                        <tr>
                                            <td>
                                                <a asp-controller="Orders" asp-action="Details" asp-route-id="@order.Id" class="text-decoration-none">
                                                    #@order.Id.ToString("D6")
                                                </a>
                                            </td>
                                            <td>@order.OrderDate.ToString("dd/MM/yyyy")</td>
                                            <td>
                                                @if (order.OrderStatus != null)
                                                {
                                                    <span class="badge bg-info">@order.OrderStatus.Name</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Chưa có</span>
                                                }
                                            </td>
                                            <td>@order.OrderDetails.Count()</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                }

                <div class="d-flex justify-content-between">
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Quay lại
                    </a>
                    <div>
                        <a asp-action="CreateOrEdit" asp-route-id="@Model.Id" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>Chỉnh sửa
                        </a>
                        <button type="button" class="btn btn-danger" 
                                onclick="showDeleteConfirm('@Url.Action("Delete", new { id = Model.Id })', 'Bạn có chắc chắn muốn xóa tài khoản &quot;@Model.UserName&quot;?')">
                            <i class="fas fa-trash me-2"></i>Xóa
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@await Html.PartialAsync("_DeleteConfirmModal")
