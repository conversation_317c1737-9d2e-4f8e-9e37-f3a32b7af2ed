@model HSU_NguyenHoangThanhSang_22207613.Models.Article

@{
    ViewData["Title"] = "Chi tiết Bài viết";
}

<div class="row">
    <div class="col-lg-10 col-md-12 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Chi tiết Bài viết
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-code me-2"></i>Mã bài viết
                            </label>
                            <div class="form-control-plaintext">
                                <span class="badge bg-secondary fs-6">@Model.Code</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-tags me-2"></i>Danh mục
                            </label>
                            <div class="form-control-plaintext">
                                <span class="badge bg-info fs-6">@Model.Category.Name</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label fw-bold">
                        <i class="fas fa-heading me-2"></i>Tiêu đề
                    </label>
                    <div class="form-control-plaintext">
                        <h4>@Model.Title</h4>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label fw-bold">
                        <i class="fas fa-file-alt me-2"></i>Nội dung
                    </label>
                    <div class="form-control-plaintext">
                        <div class="border rounded p-3 bg-light">
                            @Html.Raw(Model.Content.Replace("\n", "<br>"))
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-toggle-on me-2"></i>Trạng thái
                            </label>
                            <div class="form-control-plaintext">
                                @if (Model.IsActive)
                                {
                                    <span class="badge bg-success">Hoạt động</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">Không hoạt động</span>
                                }
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar me-2"></i>Ngày tạo
                            </label>
                            <div class="form-control-plaintext">@Model.CreatedDate.ToString("dd/MM/yyyy HH:mm")</div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between">
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Quay lại
                    </a>
                    <div>
                        <a asp-action="CreateOrEdit" asp-route-id="@Model.Id" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>Chỉnh sửa
                        </a>
                        <button type="button" class="btn btn-danger" 
                                onclick="showDeleteConfirm('@Url.Action("Delete", new { id = Model.Id })', 'Bạn có chắc chắn muốn xóa bài viết &quot;@Model.Title&quot;?')">
                            <i class="fas fa-trash me-2"></i>Xóa
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@await Html.PartialAsync("_DeleteConfirmModal")
