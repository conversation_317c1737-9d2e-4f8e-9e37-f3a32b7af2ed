@model HSU_NguyenHoangThanhSang_22207613.Models.Category

@{
    ViewData["Title"] = Model.Id == 0 ? "Thêm mới Dan<PERSON> mục" : "Chỉnh sửa Danh mục";
    var isEdit = Model.Id != 0;
}

<div class="row">
    <div class="col-lg-8 col-md-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-@(isEdit ? "edit" : "plus") me-2"></i>@ViewData["Title"]
                </h5>
            </div>
            <div class="card-body">
                <form asp-action="CreateOrEdit" method="post">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    @if (isEdit)
                    {
                        <input type="hidden" asp-for="Id" />
                        <input type="hidden" asp-for="CreatedDate" />
                    }

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Code" class="form-label">
                                    <i class="fas fa-code me-2"></i>Mã danh mục <span class="text-danger">*</span>
                                </label>
                                <input asp-for="Code" class="form-control" placeholder="Nhập mã danh mục (VD: TECH, FASHION)" />
                                <span asp-validation-for="Code" class="text-danger"></span>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Name" class="form-label">
                                    <i class="fas fa-tag me-2"></i>Tên danh mục <span class="text-danger">*</span>
                                </label>
                                <input asp-for="Name" class="form-control" placeholder="Nhập tên danh mục" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input asp-for="IsActive" class="form-check-input" type="checkbox" role="switch" />
                            <label asp-for="IsActive" class="form-check-label">
                                Trạng thái hoạt động
                            </label>
                        </div>
                        <small class="form-text text-muted">Bật để kích hoạt danh mục này</small>
                    </div>

                    @if (isEdit)
                    {
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-calendar me-2"></i>Ngày tạo
                            </label>
                            <input type="text" class="form-control" value="@Model.CreatedDate.ToString("dd/MM/yyyy HH:mm")" readonly />
                        </div>
                    }

                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Quay lại
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>@(isEdit ? "Cập nhật" : "Tạo mới")
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Auto-generate code from name
        document.getElementById('Name').addEventListener('input', function() {
            if (document.getElementById('Code').value === '') {
                var name = this.value;
                var code = name.toUpperCase()
                    .replace(/[^A-Z0-9\s]/g, '')
                    .replace(/\s+/g, '_')
                    .substring(0, 20);
                document.getElementById('Code').value = code;
            }
        });
    </script>
}
