@model HSU_NguyenHoangThanhSang_22207613.Models.Group

@{
    ViewData["Title"] = "Chi tiết Nhóm quyền";
}

<div class="row">
    <div class="col-lg-8 col-md-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Chi tiết Nhóm quyền
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-code me-2"></i>Mã nhóm
                            </label>
                            <div class="form-control-plaintext">
                                <span class="badge bg-secondary fs-6">@Model.Code</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-tag me-2"></i>Tên nhóm
                            </label>
                            <div class="form-control-plaintext">@Model.Name</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-toggle-on me-2"></i>Trạng thái
                            </label>
                            <div class="form-control-plaintext">
                                @if (Model.IsActive)
                                {
                                    <span class="badge bg-success">Hoạt động</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">Không hoạt động</span>
                                }
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar me-2"></i>Ngày tạo
                            </label>
                            <div class="form-control-plaintext">@Model.CreatedDate.ToString("dd/MM/yyyy HH:mm")</div>
                        </div>
                    </div>
                </div>

                @if (Model.Accounts != null && Model.Accounts.Any())
                {
                    <div class="mb-3">
                        <label class="form-label fw-bold">
                            <i class="fas fa-users me-2"></i>Tài khoản trong nhóm (@Model.Accounts.Count())
                        </label>
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead class="table-light">
                                    <tr>
                                        <th>Tên đăng nhập</th>
                                        <th>Họ tên</th>
                                        <th>Email</th>
                                        <th>Trạng thái</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var account in Model.Accounts.Where(a => a.IsActive))
                                    {
                                        <tr>
                                            <td>@account.UserName</td>
                                            <td>@account.FirstName @account.LastName</td>
                                            <td>@account.Email</td>
                                            <td>
                                                @if (account.IsActive)
                                                {
                                                    <span class="badge bg-success">Hoạt động</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">Không hoạt động</span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                }
                else
                {
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Chưa có tài khoản nào trong nhóm này.
                    </div>
                }

                <div class="d-flex justify-content-between">
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Quay lại
                    </a>
                    <div>
                        <a asp-action="CreateOrEdit" asp-route-id="@Model.Id" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>Chỉnh sửa
                        </a>
                        <button type="button" class="btn btn-danger" 
                                onclick="showDeleteConfirm('@Url.Action("Delete", new { id = Model.Id })', 'Bạn có chắc chắn muốn xóa nhóm quyền &quot;@Model.Name&quot;?')">
                            <i class="fas fa-trash me-2"></i>Xóa
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@await Html.PartialAsync("_DeleteConfirmModal")
