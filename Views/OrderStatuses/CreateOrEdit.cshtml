@model HSU_NguyenHoangThanhSang_22207613.Models.OrderStatus

@{
    ViewData["Title"] = Model.Id == 0 ? "Thêm mới Trạng thái đơn hàng" : "Chỉnh sửa Trạng thái đơn hàng";
    var isEdit = Model.Id != 0;
}

<div class="row">
    <div class="col-lg-8 col-md-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-@(isEdit ? "edit" : "plus") me-2"></i>@ViewData["Title"]
                </h5>
            </div>
            <div class="card-body">
                <form asp-action="CreateOrEdit" method="post">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    @if (isEdit)
                    {
                        <input type="hidden" asp-for="Id" />
                        <input type="hidden" asp-for="CreatedDate" />
                    }

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Code" class="form-label">
                                    <i class="fas fa-code me-2"></i>Mã trạng thái <span class="text-danger">*</span>
                                </label>
                                <input asp-for="Code" class="form-control" placeholder="Nhập mã trạng thái (VD: PENDING, PROCESSING)" />
                                <span asp-validation-for="Code" class="text-danger"></span>
                                <small class="form-text text-muted">
                                    Gợi ý: PENDING, PROCESSING, SHIPPED, DELIVERED, CANCELLED
                                </small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Name" class="form-label">
                                    <i class="fas fa-clipboard-list me-2"></i>Tên trạng thái <span class="text-danger">*</span>
                                </label>
                                <input asp-for="Name" class="form-control" placeholder="Nhập tên trạng thái" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-palette me-2"></i>Xem trước màu hiển thị
                        </label>
                        <div class="d-flex gap-2 flex-wrap">
                            <span class="badge bg-warning" id="preview-PENDING" style="display: none;">Chờ xử lý</span>
                            <span class="badge bg-info" id="preview-PROCESSING" style="display: none;">Đang xử lý</span>
                            <span class="badge bg-primary" id="preview-SHIPPED" style="display: none;">Đã gửi hàng</span>
                            <span class="badge bg-success" id="preview-DELIVERED" style="display: none;">Đã giao hàng</span>
                            <span class="badge bg-danger" id="preview-CANCELLED" style="display: none;">Đã hủy</span>
                            <span class="badge bg-secondary" id="preview-OTHER" style="display: none;">Khác</span>
                        </div>
                        <small class="form-text text-muted">Màu sắc sẽ tự động được chọn dựa trên mã trạng thái</small>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input asp-for="IsActive" class="form-check-input" type="checkbox" role="switch" />
                            <label asp-for="IsActive" class="form-check-label">
                                Trạng thái hoạt động
                            </label>
                        </div>
                        <small class="form-text text-muted">Bật để kích hoạt trạng thái này</small>
                    </div>

                    @if (isEdit)
                    {
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-calendar me-2"></i>Ngày tạo
                            </label>
                            <input type="text" class="form-control" value="@Model.CreatedDate.ToString("dd/MM/yyyy HH:mm")" readonly />
                        </div>
                    }

                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Quay lại
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>@(isEdit ? "Cập nhật" : "Tạo mới")
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Auto-generate code from name and show preview
        function updatePreview() {
            var code = document.getElementById('Code').value.toUpperCase();
            var name = document.getElementById('Name').value;
            
            // Hide all previews
            document.querySelectorAll('[id^="preview-"]').forEach(el => el.style.display = 'none');
            
            // Show appropriate preview
            var previewId = 'preview-' + code;
            var previewElement = document.getElementById(previewId);
            if (previewElement) {
                previewElement.textContent = name || code;
                previewElement.style.display = 'inline-block';
            } else {
                var otherPreview = document.getElementById('preview-OTHER');
                otherPreview.textContent = name || code;
                otherPreview.style.display = 'inline-block';
            }
        }
        
        document.getElementById('Name').addEventListener('input', function() {
            if (document.getElementById('Code').value === '') {
                var name = this.value.toUpperCase();
                var code = name.replace(/[^A-Z0-9]/g, '').substring(0, 15);
                document.getElementById('Code').value = code;
            }
            updatePreview();
        });
        
        document.getElementById('Code').addEventListener('input', updatePreview);
        
        // Initial preview update
        updatePreview();
    </script>
}
