@model HSU_NguyenHoangThanhSang_22207613.Models.OrderStatus

@{
    ViewData["Title"] = "Chi tiết Trạng thái đơn hàng";
}

<div class="row">
    <div class="col-lg-8 col-md-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Chi tiết Trạng thái đơn hàng
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-code me-2"></i>Mã trạng thái
                            </label>
                            <div class="form-control-plaintext">
                                @{
                                    var badgeClass = Model.Code switch
                                    {
                                        "PENDING" => "bg-warning",
                                        "PROCESSING" => "bg-info",
                                        "SHIPPED" => "bg-primary",
                                        "DELIVERED" => "bg-success",
                                        "CANCELLED" => "bg-danger",
                                        _ => "bg-secondary"
                                    };
                                }
                                <span class="badge @badgeClass fs-6">@Model.Code</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-toggle-on me-2"></i>Trạng thái
                            </label>
                            <div class="form-control-plaintext">
                                @if (Model.IsActive)
                                {
                                    <span class="badge bg-success">Hoạt động</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">Không hoạt động</span>
                                }
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label fw-bold">
                        <i class="fas fa-tag me-2"></i>Tên trạng thái
                    </label>
                    <div class="form-control-plaintext">
                        <h4>@Model.Name</h4>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label fw-bold">
                        <i class="fas fa-calendar me-2"></i>Ngày tạo
                    </label>
                    <div class="form-control-plaintext">@Model.CreatedDate.ToString("dd/MM/yyyy HH:mm")</div>
                </div>

                @if (Model.Orders != null && Model.Orders.Any())
                {
                    <div class="mb-3">
                        <label class="form-label fw-bold">
                            <i class="fas fa-shopping-cart me-2"></i>Đơn hàng có trạng thái này (@Model.Orders.Count())
                        </label>
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead class="table-light">
                                    <tr>
                                        <th>Mã đơn hàng</th>
                                        <th>Khách hàng</th>
                                        <th>Ngày đặt</th>
                                        <th>Số sản phẩm</th>
                                        <th>Ngày tạo</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var order in Model.Orders.Where(o => o.IsActive).Take(10))
                                    {
                                        <tr>
                                            <td>
                                                <a asp-controller="Orders" asp-action="Details" asp-route-id="@order.Id" class="text-decoration-none">
                                                    #@order.Id.ToString("D6")
                                                </a>
                                            </td>
                                            <td>
                                                <strong>@order.Account.FirstName @order.Account.LastName</strong>
                                                <br><small class="text-muted">@order.Account.Email</small>
                                            </td>
                                            <td>@order.OrderDate.ToString("dd/MM/yyyy")</td>
                                            <td>
                                                <span class="badge bg-info">@order.OrderDetails.Count() sản phẩm</span>
                                            </td>
                                            <td>@order.CreatedDate.ToString("dd/MM/yyyy")</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                }

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Mô tả trạng thái:</strong>
                    @switch (Model.Code)
                    {
                        case "PENDING":
                            <span>Đơn hàng đang chờ xử lý</span>
                            break;
                        case "PROCESSING":
                            <span>Đơn hàng đang được xử lý</span>
                            break;
                        case "SHIPPED":
                            <span>Đơn hàng đã được giao cho đơn vị vận chuyển</span>
                            break;
                        case "DELIVERED":
                            <span>Đơn hàng đã được giao thành công</span>
                            break;
                        case "CANCELLED":
                            <span>Đơn hàng đã bị hủy</span>
                            break;
                        default:
                            <span>Trạng thái tùy chỉnh</span>
                            break;
                    }
                </div>

                <div class="d-flex justify-content-between">
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Quay lại
                    </a>
                    <div>
                        <a asp-action="CreateOrEdit" asp-route-id="@Model.Id" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>Chỉnh sửa
                        </a>
                        <button type="button" class="btn btn-danger" 
                                onclick="showDeleteConfirm('@Url.Action("Delete", new { id = Model.Id })', 'Bạn có chắc chắn muốn xóa trạng thái &quot;@Model.Name&quot;?')">
                            <i class="fas fa-trash me-2"></i>Xóa
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@await Html.PartialAsync("_DeleteConfirmModal")
