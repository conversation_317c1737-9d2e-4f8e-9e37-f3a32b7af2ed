@model HSU_NguyenHoangThanhSang_22207613.Models.Order

@{
    ViewData["Title"] = Model.Id == 0 ? "Thêm mới <PERSON>ơn hàng" : "Chỉnh sửa Đơn hàng";
    var isEdit = Model.Id != 0;
}

<div class="row">
    <div class="col-lg-8 col-md-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-@(isEdit ? "edit" : "plus") me-2"></i>@ViewData["Title"]
                </h5>
            </div>
            <div class="card-body">
                <form asp-action="CreateOrEdit" method="post">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    @if (isEdit)
                    {
                        <input type="hidden" asp-for="Id" />
                        <input type="hidden" asp-for="CreatedDate" />
                    }

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="AccountId" class="form-label">
                                    <i class="fas fa-user me-2"></i>Khách hàng <span class="text-danger">*</span>
                                </label>
                                <select asp-for="AccountId" class="form-select" asp-items="ViewBag.AccountId">
                                    <option value="">-- Chọn khách hàng --</option>
                                </select>
                                <span asp-validation-for="AccountId" class="text-danger"></span>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="OrderDate" class="form-label">
                                    <i class="fas fa-calendar me-2"></i>Ngày đặt hàng <span class="text-danger">*</span>
                                </label>
                                <input asp-for="OrderDate" class="form-control" type="datetime-local" />
                                <span asp-validation-for="OrderDate" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="OrderStatusId" class="form-label">
                                    Trạng thái đơn hàng
                                </label>
                                <select asp-for="OrderStatusId" class="form-select" asp-items="ViewBag.OrderStatusId">
                                    <option value="">-- Chọn trạng thái --</option>
                                </select>
                                <span asp-validation-for="OrderStatusId" class="text-danger"></span>
                                <small class="form-text text-muted">Có thể để trống và cập nhật sau</small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input asp-for="IsActive" class="form-check-input" type="checkbox" role="switch" />
                                    <label asp-for="IsActive" class="form-check-label">
                                        <i class="fas fa-toggle-on me-2"></i>Trạng thái hoạt động
                                    </label>
                                </div>
                                <small class="form-text text-muted">Bật để kích hoạt đơn hàng này</small>
                            </div>
                        </div>
                    </div>

                    @if (isEdit)
                    {
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-calendar me-2"></i>Ngày tạo
                            </label>
                            <input type="text" class="form-control" value="@Model.CreatedDate.ToString("dd/MM/yyyy HH:mm")" readonly />
                        </div>
                    }

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Lưu ý:</strong> Sau khi tạo đơn hàng, bạn có thể thêm sản phẩm vào đơn hàng trong trang chi tiết đơn hàng.
                    </div>

                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Quay lại
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>@(isEdit ? "Cập nhật" : "Tạo mới")
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Set default order date to current date/time
        document.addEventListener('DOMContentLoaded', function() {
            var orderDateInput = document.getElementById('OrderDate');
            if (orderDateInput && !orderDateInput.value) {
                var now = new Date();
                now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
                orderDateInput.value = now.toISOString().slice(0, 16);
            }
        });
    </script>
}
