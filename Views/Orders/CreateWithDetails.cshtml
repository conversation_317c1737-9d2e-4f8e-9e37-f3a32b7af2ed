@model HSU_NguyenHoangThanhSang_22207613.Models.ViewModels.OrderCreateViewModel

@{
    ViewData["Title"] = "Tạo mới Đơn hàng với Chi tiết";
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus-circle me-2"></i>Tạo mới Đơn hàng với Chi tiết
                </h5>
            </div>
            <div class="card-body">
                <form asp-action="CreateWithDetails" method="post" id="orderForm">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

                    <!-- Order Information -->
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Thông tin Đơn hàng</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="AccountId" class="form-label">
                                            <i class="fas fa-user me-2"></i>Khách hàng <span
                                                class="text-danger">*</span>
                                        </label>
                                        <select asp-for="AccountId" class="form-select" asp-items="ViewBag.AccountId">
                                            <option value="">-- Chọn khách hàng --</option>
                                        </select>
                                        <span asp-validation-for="AccountId" class="text-danger"></span>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="OrderDate" class="form-label">
                                            <i class="fas fa-calendar me-2"></i>Ngày đặt hàng <span
                                                class="text-danger">*</span>
                                        </label>
                                        <input asp-for="OrderDate" class="form-control" type="datetime-local" />
                                        <span asp-validation-for="OrderDate" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="OrderStatusId" class="form-label">
                                            Trạng thái đơn hàng
                                        </label>
                                        <select asp-for="OrderStatusId" class="form-select"
                                            asp-items="ViewBag.OrderStatusId">
                                            <option value="">-- Chọn trạng thái --</option>
                                        </select>
                                        <span asp-validation-for="OrderStatusId" class="text-danger"></span>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check form-switch mt-4">
                                            <input asp-for="IsActive" class="form-check-input" type="checkbox"
                                                role="switch" />
                                            <label asp-for="IsActive" class="form-check-label">
                                                Trạng thái hoạt động
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Details -->
                    <div class="card mb-4">
                        <div
                            class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-list-alt me-2"></i>Chi tiết Đơn hàng</h6>
                            <button type="button" class="btn btn-light btn-sm" onclick="addOrderDetail()">
                                <i class="fas fa-plus me-1"></i>Thêm sản phẩm
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="orderDetailsContainer">
                                <!-- Order details will be added here dynamically -->
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Hướng dẫn:</strong> Nhấn "Thêm sản phẩm" để thêm sản phẩm vào đơn hàng.
                                        Bạn có thể thêm nhiều sản phẩm khác nhau.
                                    </div>
                                </div>
                            </div>

                            <!-- Total -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h5 class="mb-0">Tổng cộng:</h5>
                                                <h4 class="mb-0 text-primary" id="grandTotal">0 ₫</h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Quay lại
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Tạo đơn hàng
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }

    <script>
        let detailIndex = 0;

        // Products data from server
        const originalProducts = @Html.Raw(Json.Serialize(ViewBag.Products ?? new { }));
        const products = Object.entries(originalProducts).reduce((product, [key, value]) => {
            product[key] = {
                Name: value.name,
                Price: value.price
            };
            return product;
        }, {});

        // Set default order date
        document.addEventListener('DOMContentLoaded', function () {

            var orderDateInput = document.getElementById('OrderDate');
            if (orderDateInput && !orderDateInput.value) {
                var now = new Date();
                now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
                orderDateInput.value = now.toISOString().slice(0, 16);
            }

            // Add first order detail row
            addOrderDetail();
        });

        function addOrderDetail() {
            const container = document.getElementById('orderDetailsContainer');

            if (!container) {
                return;
            }

            // Build product options
            let productOptions = '<option value="">-- Chọn sản phẩm --</option>';

            if (products && typeof products === 'object' && Object.keys(products).length > 0) {
                Object.keys(products).forEach(productId => {
                    const product = products[productId];
                    if (product && product.Name && product.Price !== undefined) {
                        productOptions += `<option value="${productId}">${product.Name} - ${product.Price.toLocaleString('vi-VN')} ₫</option>`;
                    }
                });
            } else {
                productOptions += '<option value="">Không có sản phẩm nào</option>';
            }

            const detailHtml = `
                    <div class="row mb-3 order-detail-row" data-index="${detailIndex}">
                        <div class="col-md-5">
                            <label class="form-label">Sản phẩm</label>
                            <select name="OrderDetails[${detailIndex}].ProductId" class="form-select product-select" onchange="updatePrice(${detailIndex})">
                                ${productOptions}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Đơn giá</label>
                            <input type="text" class="form-control price-display" readonly placeholder="0 ₫">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Số lượng</label>
                            <input name="OrderDetails[${detailIndex}].Quantity" type="number" class="form-control quantity-input" min="1" value="1" onchange="updateTotal()">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Thành tiền</label>
                            <input type="text" class="form-control total-display" readonly placeholder="0 ₫">
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-danger btn-sm d-block" onclick="removeOrderDetail(${detailIndex})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;

            container.insertAdjacentHTML('beforeend', detailHtml);
            detailIndex++;
        }

        function removeOrderDetail(index) {
            const row = document.querySelector(`[data-index="${index}"]`);
            if (row) {
                row.remove();
                updateTotal();
            }
        }

        function updatePrice(index) {
            const row = document.querySelector(`[data-index="${index}"]`);
            const productSelect = row.querySelector('.product-select');
            const priceDisplay = row.querySelector('.price-display');
            const quantityInput = row.querySelector('.quantity-input');

            if (productSelect.value && products[productSelect.value]) {
                const price = products[productSelect.value].Price;
                priceDisplay.value = price.toLocaleString('vi-VN') + ' ₫';
                updateTotal();
            } else {
                priceDisplay.value = '0 ₫';
                updateTotal();
            }
        }

        function updateTotal() {
            let grandTotal = 0;

            document.querySelectorAll('.order-detail-row').forEach(row => {
                const productSelect = row.querySelector('.product-select');
                const quantityInput = row.querySelector('.quantity-input');
                const totalDisplay = row.querySelector('.total-display');

                if (productSelect.value && products[productSelect.value] && quantityInput.value) {
                    const price = products[productSelect.value].Price;
                    const quantity = parseInt(quantityInput.value) || 0;
                    const total = price * quantity;

                    totalDisplay.value = total.toLocaleString('vi-VN') + ' ₫';
                    grandTotal += total;
                } else {
                    totalDisplay.value = '0 ₫';
                }
            });

            document.getElementById('grandTotal').textContent = grandTotal.toLocaleString('vi-VN') + ' ₫';
        }
    </script>
}
