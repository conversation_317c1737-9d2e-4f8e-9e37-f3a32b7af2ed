@model HSU_NguyenHoangThanhSang_22207613.Models.Order

@{
    ViewData["Title"] = "Chi tiết Đơn hàng";
}

<div class="row">
    <div class="col-12">
        <!-- Order Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Thông tin Đơn hàng #@Model.Id.ToString("D6")
                </h5>
            </div>
            <div class="card-body">
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                }
                
                @if (TempData["ErrorMessage"] != null)
                {
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>@TempData["ErrorMessage"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                }

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-user me-2"></i>Khách hàng
                            </label>
                            <div class="form-control-plaintext">
                                <strong>@Model.Account.FirstName @Model.Account.LastName</strong><br>
                                <small class="text-muted">@Model.Account.Email</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar me-2"></i>Ngày đặt hàng
                            </label>
                            <div class="form-control-plaintext">@Model.OrderDate.ToString("dd/MM/yyyy HH:mm")</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-clipboard-list me-2"></i>Trạng thái hiện tại
                            </label>
                            <div class="form-control-plaintext">
                                @if (Model.OrderStatus != null)
                                {
                                    var badgeClass = Model.OrderStatus.Code switch
                                    {
                                        "PENDING" => "bg-warning",
                                        "PROCESSING" => "bg-info",
                                        "SHIPPED" => "bg-primary",
                                        "DELIVERED" => "bg-success",
                                        "CANCELLED" => "bg-danger",
                                        _ => "bg-secondary"
                                    };
                                    <span class="badge @badgeClass fs-6">@Model.OrderStatus.Name</span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary fs-6">Chưa có trạng thái</span>
                                }
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-sync-alt me-2"></i>Cập nhật trạng thái
                            </label>
                            <form method="post" asp-action="UpdateStatus" class="d-flex gap-2">
                                <input type="hidden" name="orderId" value="@Model.Id" />
                                <select name="orderStatusId" class="form-select" asp-items="ViewBag.OrderStatusId">
                                    <option value="">-- Chọn trạng thái --</option>
                                </select>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>Cập nhật
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Details -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list-alt me-2"></i>Chi tiết Đơn hàng (@Model.OrderDetails.Count() sản phẩm)
                </h5>
            </div>
            <div class="card-body">
                @if (Model.OrderDetails.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead class="table-light">
                                <tr>
                                    <th>Sản phẩm</th>
                                    <th>Đơn giá</th>
                                    <th>Số lượng</th>
                                    <th>Thành tiền</th>
                                </tr>
                            </thead>
                            <tbody>
                                @{
                                    decimal totalAmount = 0;
                                }
                                @foreach (var detail in Model.OrderDetails.Where(od => od.IsActive))
                                {
                                    var itemTotal = detail.Product.Price * detail.Quantity;
                                    totalAmount += itemTotal;
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if (!string.IsNullOrEmpty(detail.Product.Thumbnail))
                                                {
                                                    <img src="@detail.Product.Thumbnail" alt="@detail.Product.Name" class="img-thumbnail me-2" style="width: 40px; height: 40px; object-fit: cover;" />
                                                }
                                                <div>
                                                    <strong>@detail.Product.Name</strong>
                                                    <br><small class="text-muted">@(detail.Product.Category?.Name ?? "Chưa phân loại")</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>@detail.Product.Price.ToString("N0")</td>
                                        <td>
                                            <span class="badge bg-info">@detail.Quantity</span>
                                        </td>
                                        <td>
                                            <strong>@itemTotal.ToString("N0") ₫</strong>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <th colspan="2" class="text-end">Tổng cộng:</th>
                                    <th>@totalAmount.ToString("N0") ₫</th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-2x mb-2"></i><br>
                        Đơn hàng này chưa có sản phẩm nào.
                    </div>
                }

                <div class="d-flex justify-content-between mt-3">
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Quay lại
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@await Html.PartialAsync("_DeleteConfirmModal")

@section Scripts {
    <script>
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
}
