@model IEnumerable<HSU_NguyenHoangThanhSang_22207613.Models.Order>

@{
    ViewData["Title"] = "Quản lý Đơn hàng";
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>Danh sách Đơn hàng
                </h5>
                <div>
                    <a asp-action="CreateWithDetails" class="btn btn-success me-2">
                        <i class="fas fa-plus-circle me-2"></i>Tạo đơn hàng + Chi tiết
                    </a>
                    <a asp-action="CreateOrEdit" class="btn btn-primary me-2">
                        <i class="fas fa-plus me-2"></i>Tạo đơn hàng đơn giản
                    </a>
                </div>
            </div>
            <div class="card-body">
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                }
                
                @if (TempData["ErrorMessage"] != null)
                {
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>@TempData["ErrorMessage"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                }

                @await Html.PartialAsync("_SearchBox")

                @if (Model.Any())
                {
                    @foreach (var item in Model)
                    {
                        <div class="card mb-3 @(item.IsActive ? "" : "deleted-order")" style="@(item.IsActive ? "" : "opacity: 0.6; border-left: 4px solid #dc3545;")">
                            <div class="card-header">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h6 class="mb-0">
                                            <span class="badge bg-primary me-2">#@item.Id.ToString("D6")</span>
                                            <strong>@item.Account.FirstName @item.Account.LastName</strong>
                                            <small class="text-muted">(@item.Account.Email)</small>
                                        </h6>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>@item.OrderDate.ToString("dd/MM/yyyy HH:mm")
                                            @if (item.OrderStatus != null)
                                            {
                                                var badgeClass = item.OrderStatus.Code switch
                                                {
                                                    "PENDING" => "bg-warning",
                                                    "PROCESSING" => "bg-info",
                                                    "SHIPPED" => "bg-primary",
                                                    "DELIVERED" => "bg-success",
                                                    "CANCELLED" => "bg-danger",
                                                    _ => "bg-secondary"
                                                };
                                                <span class="badge @badgeClass ms-2">@item.OrderStatus.Name</span>
                                            }
                                            @if (!item.IsActive)
                                            {
                                                <span class="badge bg-danger ms-2">ĐÃ XÓA</span>
                                            }
                                        </small>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <div class="btn-group" role="group">
                                            <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-sm btn-info" title="Xem chi tiết">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if (item.IsActive)
                                            {
                                                <a asp-action="CreateOrEdit" asp-route-id="@item.Id" class="btn btn-sm btn-warning" title="Chỉnh sửa">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-danger" title="Xóa"
                                                        onclick="showDeleteConfirm('@Url.Action("Delete", new { id = item.Id })', 'Bạn có chắc chắn muốn xóa đơn hàng #@item.Id.ToString("D6") và tất cả chi tiết đơn hàng?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            }
                                            else
                                            {
                                                <a asp-action="Restore" asp-route-id="@item.Id" class="btn btn-sm btn-success" title="Khôi phục"
                                                   onclick="return confirm('Bạn có chắc chắn muốn khôi phục đơn hàng #@item.Id.ToString("D6") và tất cả chi tiết đơn hàng?')">
                                                    <i class="fas fa-undo"></i>
                                                </a>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                @if (item.OrderDetails.Any())
                                {
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>Sản phẩm</th>
                                                    <th>Đơn giá</th>
                                                    <th>Số lượng</th>
                                                    <th>Thành tiền</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @{
                                                    decimal orderTotal = 0;
                                                }
                                                @foreach (var detail in item.OrderDetails.Where(od => od.IsActive))
                                                {
                                                    var itemTotal = (detail.Product?.Price ?? 0) * detail.Quantity;
                                                    orderTotal += itemTotal;
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                @if (detail.Product != null && !string.IsNullOrEmpty(detail.Product.Thumbnail))
                                                                {
                                                                    <img src="@detail.Product.Thumbnail" alt="@detail.Product.Name" class="img-thumbnail me-2" style="width: 30px; height: 30px; object-fit: cover;" />
                                                                }
                                                                <div>
                                                                    <strong>@(detail.Product?.Name ?? "Sản phẩm không xác định")</strong>
                                                                    <br><small class="text-muted">@(detail.Product?.Category?.Name ?? "Chưa phân loại")</small>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>@(detail.Product?.Price.ToString("N0") ?? "0") ₫</td>
                                                        <td><span class="badge bg-info">@detail.Quantity</span></td>
                                                        <td><strong>@itemTotal.ToString("N0") ₫</strong></td>
                                                    </tr>
                                                }
                                            </tbody>
                                            <tfoot class="table-light">
                                                <tr>
                                                    <th colspan="3" class="text-end">Tổng cộng:</th>
                                                    <th>@orderTotal.ToString("N0") ₫</th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                }
                                else
                                {
                                    <div class="text-center text-muted py-2">
                                        <i class="fas fa-inbox me-2"></i>Đơn hàng này chưa có sản phẩm nào.
                                    </div>
                                }
                            </div>
                        </div>
                    }
                }
                else
                {
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-inbox fa-3x mb-3"></i><br>
                        <h5>Chưa có dữ liệu đơn hàng nào.</h5>
                        <p>Hãy tạo đơn hàng đầu tiên của bạn!</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@await Html.PartialAsync("_DeleteConfirmModal")

@section Scripts {
    <script>
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);

        function toggleDeletedOrders() {
            const checkbox = document.getElementById('showDeletedOrders');
            const deletedRows = document.querySelectorAll('tr.deleted-order');

            deletedRows.forEach(row => {
                if (checkbox.checked) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // Ẩn đơn hàng đã xóa khi load trang
        document.addEventListener('DOMContentLoaded', function() {
            toggleDeletedOrders();
        });
    </script>
}
