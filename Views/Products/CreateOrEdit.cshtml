@model HSU_NguyenHoangThanhSang_22207613.Models.Product

@{
    ViewData["Title"] = Model.Id == 0 ? "Thêm mới <PERSON>ản phẩm" : "Chỉnh sửa <PERSON>ản phẩm";
    var isEdit = Model.Id != 0;
}

<div class="row">
    <div class="col-lg-10 col-md-12 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-@(isEdit ? "edit" : "plus") me-2"></i>@ViewData["Title"]
                </h5>
            </div>
            <div class="card-body">
                <form asp-action="CreateOrEdit" method="post" enctype="multipart/form-data">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    @if (isEdit)
                    {
                        <input type="hidden" asp-for="Id" />
                        <input type="hidden" asp-for="CreatedDate" />
                        <input type="hidden" asp-for="Thumbnail" />
                    }

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="SKU" class="form-label">
                                    <i class="fas fa-barcode me-2"></i>SKU <span class="text-danger">*</span>
                                </label>
                                <input asp-for="SKU" class="form-control" placeholder="Nhập mã SKU" />
                                <span asp-validation-for="SKU" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Name" class="form-label">
                                    <i class="fas fa-box me-2"></i>Tên sản phẩm <span class="text-danger">*</span>
                                </label>
                                <input asp-for="Name" class="form-control" placeholder="Nhập tên sản phẩm" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label asp-for="Description" class="form-label">
                                    <i class="fas fa-align-left me-2"></i>Mô tả sản phẩm
                                </label>
                                <textarea asp-for="Description" class="form-control" rows="4" placeholder="Nhập mô tả chi tiết về sản phẩm..."></textarea>
                                <span asp-validation-for="Description" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Price" class="form-label">
                                    <i class="fas fa-dollar-sign me-2"></i>Giá <span class="text-danger">*</span>
                                </label>
                                <input asp-for="Price" class="form-control" type="number" step="0.01" placeholder="Nhập giá sản phẩm" />
                                <span asp-validation-for="Price" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <!-- Placeholder for future field or keep empty for layout balance -->
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label asp-for="CategoryId" class="form-label">
                                    <i class="fas fa-tags me-2"></i>Danh mục <span class="text-danger">*</span>
                                </label>
                                <select asp-for="CategoryId" class="form-select" asp-items="ViewBag.CategoryId">
                                    <option value="">-- Chọn danh mục --</option>
                                </select>
                                <span asp-validation-for="CategoryId" class="text-danger"></span>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label asp-for="SupplierId" class="form-label">
                                    <i class="fas fa-truck me-2"></i>Nhà cung cấp
                                </label>
                                <select asp-for="SupplierId" class="form-select" asp-items="ViewBag.SupplierId">
                                    <option value="">-- Chọn nhà cung cấp --</option>
                                </select>
                                <span asp-validation-for="SupplierId" class="text-danger"></span>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label asp-for="ProductTypeId" class="form-label">
                                    <i class="fas fa-layer-group me-2"></i>Loại sản phẩm
                                </label>
                                <select asp-for="ProductTypeId" class="form-select" asp-items="ViewBag.ProductTypeId">
                                    <option value="">-- Chọn loại sản phẩm --</option>
                                </select>
                                <span asp-validation-for="ProductTypeId" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="thumbnailFile" class="form-label">
                            <i class="fas fa-image me-2"></i>Hình ảnh sản phẩm
                        </label>
                        <input type="file" class="form-control" id="thumbnailFile" name="thumbnailFile" accept="image/*" />
                        <small class="form-text text-muted">Chọn file hình ảnh (JPG, PNG, GIF)</small>
                        
                        @if (isEdit && !string.IsNullOrEmpty(Model.Thumbnail))
                        {
                            <div class="mt-2">
                                <label class="form-label">Hình ảnh hiện tại:</label><br>
                                <img src="@Model.Thumbnail" alt="Current thumbnail" class="img-thumbnail" style="max-width: 200px; max-height: 200px;" />
                            </div>
                        }
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input asp-for="IsActive" class="form-check-input" type="checkbox" role="switch" />
                            <label asp-for="IsActive" class="form-check-label">
                                Trạng thái hoạt động
                            </label>
                        </div>
                        <small class="form-text text-muted">Bật để kích hoạt sản phẩm này</small>
                    </div>

                    @if (isEdit)
                    {
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-calendar me-2"></i>Ngày tạo
                            </label>
                            <input type="text" class="form-control" value="@Model.CreatedDate.ToString("dd/MM/yyyy HH:mm")" readonly />
                        </div>
                    }

                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Quay lại
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>@(isEdit ? "Cập nhật" : "Tạo mới")
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Preview image before upload
        document.getElementById('thumbnailFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Create or update preview image
                    let preview = document.getElementById('imagePreview');
                    if (!preview) {
                        preview = document.createElement('img');
                        preview.id = 'imagePreview';
                        preview.className = 'img-thumbnail mt-2';
                        preview.style.maxWidth = '200px';
                        preview.style.maxHeight = '200px';
                        e.target.parentNode.appendChild(preview);
                    }
                    preview.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    </script>
}
