@model IEnumerable<HSU_NguyenHoangThanhSang_22207613.Models.Product>

@{
    ViewData["Title"] = "Quản lý <PERSON>n phẩm";
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-box me-2"></i>Danh sách Sản phẩm
                </h5>
                <a asp-action="CreateOrEdit" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Thêm mới
                </a>
            </div>
            <div class="card-body">
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                }
                
                @if (TempData["ErrorMessage"] != null)
                {
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>@TempData["ErrorMessage"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                }

                @await Html.PartialAsync("_SearchBox")

                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Hình ảnh</th>
                                <th>Tên sản phẩm</th>
                                <th>Giá</th>
                                <th>Danh mục</th>
                                <th>Nhà cung cấp</th>
                                <th>Trạng thái</th>
                                <th class="text-center">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (Model.Any())
                            {
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.Thumbnail))
                                            {
                                                <img src="@item.Thumbnail" alt="@item.Name" class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;" />
                                            }
                                            else
                                            {
                                                <div class="bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                    <i class="fas fa-image text-muted"></i>
                                                </div>
                                            }
                                        </td>
                                        <td>
                                            <strong>@item.Name</strong>
                                            @if (item.ProductType != null)
                                            {
                                                <br><small class="text-muted">@item.ProductType.Name</small>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge bg-success">@item.Price.ToString("C0")</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">@item.Category.Name</span>
                                        </td>
                                        <td>
                                            @if (item.Supplier != null)
                                            {
                                                <span class="badge bg-secondary">@item.Supplier.Name</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">Chưa có</span>
                                            }
                                        </td>
                                        <td>
                                            @if (item.IsActive)
                                            {
                                                <span class="badge bg-success">Hoạt động</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-danger">Không hoạt động</span>
                                            }
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-sm btn-info" title="Xem chi tiết">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="CreateOrEdit" asp-route-id="@item.Id" class="btn btn-sm btn-warning" title="Chỉnh sửa">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-danger" title="Xóa"
                                                        onclick="showDeleteConfirm('@Url.Action("Delete", new { id = item.Id })', 'Bạn có chắc chắn muốn xóa sản phẩm &quot;@item.Name&quot;?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            }
                            else
                            {
                                <tr>
                                    <td colspan="7" class="text-center text-muted py-4">
                                        <i class="fas fa-inbox fa-2x mb-2"></i><br>
                                        Chưa có dữ liệu sản phẩm nào.
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@await Html.PartialAsync("_DeleteConfirmModal")

@section Scripts {
    <script>
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
}
