﻿<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - HSU Management System</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />

    <style>
        :root {
            --sidebar-width: 250px;
            --primary-color: #667eea;
            --secondary-color: #764ba2;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-menu {
            padding: 1rem 0;
        }

        .sidebar-menu .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1.5rem;
            border: none;
            transition: all 0.3s;
        }

        .sidebar-menu .nav-link:hover,
        .sidebar-menu .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            border-left: 3px solid white;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
        }

        .top-navbar {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 1rem 2rem;
            margin-bottom: 2rem;
        }

        .content-wrapper {
            padding: 0 2rem 2rem 2rem;
        }

        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
        }

        @@media (max-width: 768px) {
            .sidebar {
                margin-left: calc(-1 * var(--sidebar-width));
            }

            .sidebar.show {
                margin-left: 0;
            }

            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fa-sharp-duotone fa-solid fa-landmark me-2"></i>Ecommerce</h4>
            <small>Management Dashboard</small>
        </div>

        <div class="sidebar-menu">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" asp-controller="Home" asp-action="Index">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                </li>

                <li class="nav-item">
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>Quản lý hệ thống</span>
                    </h6>
                </li>

                <li class="nav-item">
                    <a class="nav-link" asp-controller="Groups" asp-action="Index">
                        <i class="fas fa-users-cog me-2"></i>Nhóm quyền
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link" asp-controller="Accounts" asp-action="Index">
                        <i class="fas fa-user-friends me-2"></i>Tài khoản
                    </a>
                </li>

                <li class="nav-item">
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>Quản lý nội dung</span>
                    </h6>
                </li>

                <li class="nav-item">
                    <a class="nav-link" asp-controller="Categories" asp-action="Index">
                        <i class="fas fa-tags me-2"></i>Danh mục
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link" asp-controller="Articles" asp-action="Index">
                        <i class="fas fa-newspaper me-2"></i>Bài viết
                    </a>
                </li>

                <li class="nav-item">
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>Quản lý sản phẩm</span>
                    </h6>
                </li>

                <li class="nav-item">
                    <a class="nav-link" asp-controller="Suppliers" asp-action="Index">
                        <i class="fas fa-truck me-2"></i>Nhà cung cấp
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link" asp-controller="ProductTypes" asp-action="Index">
                        <i class="fas fa-layer-group me-2"></i>Loại sản phẩm
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link" asp-controller="Products" asp-action="Index">
                        <i class="fas fa-box me-2"></i>Sản phẩm
                    </a>
                </li>

                <li class="nav-item">
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>Quản lý đơn hàng</span>
                    </h6>
                </li>

                <li class="nav-item">
                    <a class="nav-link" asp-controller="OrderStatuses" asp-action="Index">
                        <i class="fas fa-clipboard-list me-2"></i>Trạng thái đơn hàng
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link" asp-controller="Orders" asp-action="Index">
                        <i class="fas fa-shopping-cart me-2"></i>Đơn hàng
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link" asp-controller="OrderDetails" asp-action="Index">
                        <i class="fas fa-list-alt me-2"></i>Chi tiết đơn hàng
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <nav class="top-navbar d-flex justify-content-between align-items-center">
            <div>
                <button class="btn btn-link d-md-none" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h5 class="mb-0">@ViewData["Title"]</h5>
            </div>

            <div class="dropdown">
                <button class="btn btn-link dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user-circle me-2"></i>@ViewBag.FullName
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><h6 class="dropdown-header">@ViewBag.GroupName</h6></li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <form method="post" asp-controller="Auth" asp-action="Logout" class="d-inline">
                            <button type="submit" class="dropdown-item">
                                <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Content -->
        <div class="content-wrapper">
            @RenderBody()
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    <script>
        // Sidebar toggle for mobile
        document.getElementById('sidebarToggle')?.addEventListener('click', function() {
            document.querySelector('.sidebar').classList.toggle('show');
        });

        // Active menu highlighting
        const currentPath = window.location.pathname;
        const menuLinks = document.querySelectorAll('.sidebar-menu .nav-link');
        menuLinks.forEach(link => {
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
            }
        });
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
