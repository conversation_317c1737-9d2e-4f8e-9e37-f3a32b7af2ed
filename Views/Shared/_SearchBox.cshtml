@{
    string searchValue = ViewBag.SearchTerm as string ?? "";
    string placeholder = ViewBag.SearchPlaceholder as string ?? "Tìm kiếm...";
    string actionName = ViewBag.SearchAction as string ?? "Index";
    string controllerName = ViewBag.SearchController as string ?? ViewContext.RouteData.Values["controller"]?.ToString();
}

<div class="row mb-3">
    <div class="col-md-6">
        <form method="get" action="@Url.Action(actionName, controllerName)" class="d-flex">
            <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                    <i class="fas fa-search text-muted"></i>
                </span>
                <input type="text" 
                       name="searchTerm" 
                       value="@searchValue" 
                       class="form-control border-start-0" 
                       placeholder="@placeholder"
                       style="border-left: none !important;">
                <button type="submit" class="btn btn-outline-secondary">
                    <i class="fas fa-search me-1"></i>T<PERSON><PERSON> kiếm
                </button>
                @if (!string.IsNullOrEmpty(searchValue))
                {
                    <a href="@Url.Action(actionName, controllerName)" class="btn btn-outline-danger" title="Xóa bộ lọc">
                        <i class="fas fa-times"></i>
                    </a>
                }
            </div>
        </form>
    </div>
    @if (!string.IsNullOrEmpty(searchValue))
    {
        <div class="col-md-6">
            <div class="alert alert-info mb-0 py-2">
                <i class="fas fa-info-circle me-2"></i>
                Đang tìm kiếm: <strong>"@searchValue"</strong>
                <a href="@Url.Action(actionName, controllerName)" class="text-decoration-none ms-2">
                    <i class="fas fa-times"></i> Xóa
                </a>
            </div>
        </div>
    }
</div>
