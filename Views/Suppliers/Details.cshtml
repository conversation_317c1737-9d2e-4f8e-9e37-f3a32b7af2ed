@model HSU_NguyenHoangThanhSang_22207613.Models.Supplier

@{
    ViewData["Title"] = "Chi tiết Nhà cung cấp";
}

<div class="row">
    <div class="col-lg-8 col-md-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Chi tiết Nhà cung cấp
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-code me-2"></i>Mã nhà cung cấp
                            </label>
                            <div class="form-control-plaintext">
                                <span class="badge bg-secondary fs-6">@Model.Code</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-toggle-on me-2"></i>Trạng thái
                            </label>
                            <div class="form-control-plaintext">
                                @if (Model.IsActive)
                                {
                                    <span class="badge bg-success">Hoạt động</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">Không hoạt động</span>
                                }
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label fw-bold">
                        <i class="fas fa-truck me-2"></i>Tên nhà cung cấp
                    </label>
                    <div class="form-control-plaintext">
                        <h4>@Model.Name</h4>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label fw-bold">
                        <i class="fas fa-calendar me-2"></i>Ngày tạo
                    </label>
                    <div class="form-control-plaintext">@Model.CreatedDate.ToString("dd/MM/yyyy HH:mm")</div>
                </div>

                @if (Model.Products != null && Model.Products.Any())
                {
                    <div class="mb-3">
                        <label class="form-label fw-bold">
                            <i class="fas fa-box me-2"></i>Sản phẩm từ nhà cung cấp này (@Model.Products.Count())
                        </label>
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead class="table-light">
                                    <tr>
                                        <th>Hình ảnh</th>
                                        <th>Tên sản phẩm</th>
                                        <th>Danh mục</th>
                                        <th>Loại</th>
                                        <th>Giá</th>
                                        <th>Trạng thái</th>
                                        <th>Ngày tạo</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var product in Model.Products.Where(p => p.IsActive).Take(10))
                                    {
                                        <tr>
                                            <td>
                                                @if (!string.IsNullOrEmpty(product.Thumbnail))
                                                {
                                                    <img src="@product.Thumbnail" alt="@product.Name" class="img-thumbnail" style="width: 40px; height: 40px; object-fit: cover;" />
                                                }
                                                else
                                                {
                                                    <div class="bg-light d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                        <i class="fas fa-image text-muted"></i>
                                                    </div>
                                                }
                                            </td>
                                            <td>
                                                <a asp-controller="Products" asp-action="Details" asp-route-id="@product.Id" class="text-decoration-none">
                                                    <strong>@product.Name</strong>
                                                </a>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@product.Category.Name</span>
                                            </td>
                                            <td>
                                                @if (product.ProductType != null)
                                                {
                                                    <span class="badge bg-secondary">@product.ProductType.Name</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Chưa phân loại</span>
                                                }
                                            </td>
                                            <td><strong>@product.Price.ToString("C0")</strong></td>
                                            <td>
                                                @if (product.IsActive)
                                                {
                                                    <span class="badge bg-success">Hoạt động</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">Không hoạt động</span>
                                                }
                                            </td>
                                            <td>@product.CreatedDate.ToString("dd/MM/yyyy")</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                }

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Thống kê:</strong>
                    @if (Model.Products != null && Model.Products.Any())
                    {
                        var totalProducts = Model.Products.Count(p => p.IsActive);
                        var totalValue = Model.Products.Where(p => p.IsActive).Sum(p => p.Price);
                        var avgPrice = totalProducts > 0 ? totalValue / totalProducts : 0;
                        <span>Cung cấp <strong>@totalProducts</strong> sản phẩm với tổng giá trị <strong>@totalValue.ToString("C0")</strong> (trung bình <strong>@avgPrice.ToString("C0")</strong>/sản phẩm)</span>
                    }
                    else
                    {
                        <span>Chưa có sản phẩm nào từ nhà cung cấp này.</span>
                    }
                </div>

                <div class="d-flex justify-content-between">
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Quay lại
                    </a>
                    <div>
                        <a asp-action="CreateOrEdit" asp-route-id="@Model.Id" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>Chỉnh sửa
                        </a>
                        <button type="button" class="btn btn-danger" 
                                onclick="showDeleteConfirm('@Url.Action("Delete", new { id = Model.Id })', 'Bạn có chắc chắn muốn xóa nhà cung cấp &quot;@Model.Name&quot;?')">
                            <i class="fas fa-trash me-2"></i>Xóa
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@await Html.PartialAsync("_DeleteConfirmModal")
