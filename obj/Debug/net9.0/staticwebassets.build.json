{"Version": 1, "Hash": "fMWL4MK2Hv2R0LEisyiQcm1xk0WbhMwBGRkcZ3qUWzE=", "Source": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>g<PERSON>hanhSang_22207613/wwwroot", "Source": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "Pattern": "**"}], "Assets": [{"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/06lgtu8qye-aexeepp0ev.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4jymmnvikd", "Integrity": "UFok8WEocoom8UMWS+es3N9UMNaqVZdnOD5Mbxj/F/s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/0hn9qlz7yz-47otxtyo56.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4zfcth82gc", "Integrity": "4PypidGgV/pb6NTdbW3MxWNol0BeoGyycmZym1ulQac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/0mlfmn3zcy-d7shbmvgxk.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nxng5xh76p", "Integrity": "K/ZIAouXSbIcQFaQIVnwVXQB7VwD/k3K0f1FLrpT1DA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/1azoxyujlq-b7pk76d08c.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ovpq8q8n2s", "Integrity": "fAr5V6ldLYzgCQCrnJo6Cu7m2UEdDJE1pQ0wbhaBtUo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/3dipqyym91-x0q3zqp4vz.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lsrbc0hkbt", "Integrity": "df0X2bOJ06A+a9mf19qzQWvt7CCNnAaCX0yWSHwxdUE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/3gtazo3ge3-4vt8tgilx7.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Computed", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613#[.{fingerprint=4vt8tgilx7}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/scopedcss/bundle/HSU-NguyenHoangThanhSang_22207613.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0ts550hw3u", "Integrity": "7Kno3CEt/4W+3JvR4jpoKmAwaV4vlM73YudkzfUnvzI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/scopedcss/bundle/HSU-NguyenHoangThanhSang_22207613.styles.css", "FileLength": 557, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/3swqra1zoe-4v8eqarkd7.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nu9eu9tcct", "Integrity": "X5v6p2DF+RAiFhn86edCn2BMm85rL/1BfawJ7eYKiaY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/46sb2pcnts-6pdc2jztkx.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "alm6no8o3r", "Integrity": "3g4vdMAw6KkaWMu7zxiegTSQXuIuaVWaJntJNk6Do5k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/4f5ighps57-63fj8s7r0e.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vj2ybz069h", "Integrity": "axjpMrpO28RCO+lbVo53yegI7s+b3vU0DGRS48HcQ8U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/599ujbjjs2-4vt8tgilx7.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Computed", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613#[.{fingerprint=4vt8tgilx7}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/scopedcss/projectbundle/HSU-NguyenHoangThanhSang_22207613.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0ts550hw3u", "Integrity": "7Kno3CEt/4W+3JvR4jpoKmAwaV4vlM73YudkzfUnvzI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/scopedcss/projectbundle/HSU-NguyenHoangThanhSang_22207613.bundle.scp.css", "FileLength": 557, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/6qqdvnpsj0-ft3s53vfgj.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "swt17hl2xb", "Integrity": "rGg/dX06muNMQeG+lvJ69DzvgGZZqrpPVSkJcrP/VHE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/6v6cpq9nz0-c2oey78nd0.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bc08mbyxz7", "Integrity": "RQr8+RwL5iy9y9f4CBQTW/ZvhfqawQ+e/L2FVneMUeI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/7d25u8026p-37tfw0ft22.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jn9pdzi7fo", "Integrity": "H3YVfeyTRT1+*******************************=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/8k3s542c9c-fvhpjtyr6v.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tq4za9huo2", "Integrity": "xaCEzyQKiV85d/qRa4kxMNFkjAE8f+xqfcs47A+bU98=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/8nqm6i12zt-o1o13a6vjx.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fjz6w14ce3", "Integrity": "lQJ7hfi78sXWfeYzRPHYaorepUtSAgUubKKf7dCETlA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/9d9mvyxx09-hrwsygsryq.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "efj6o7965p", "Integrity": "JrRzHeTjl4b6Uz7Bw07aa6fJXaq6cvJS2O5NNTMvdsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/ad1sftlax3-j5mq2jizvt.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "93s9kmgeq8", "Integrity": "i93LDQ234gPIkUQkIc0/9sJEzqXyf8EBOksfgYR30V4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/beh60p7esr-lcd1t2u6c8.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t9ewjjmmfp", "Integrity": "v/CvnuCD/wTHczgdFZP6S3KwG2qp/CQDVwOg5+Uuk30=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/bg0bosjvju-87fc7y1x7t.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6yehlwgclf", "Integrity": "7hpoe4sIU0dU1PutwtmtqeLFZasIJnQeD5iGeXrySb4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/cgvb40ww4i-rzd6atqjts.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "50vvwv16jm", "Integrity": "tTpkoOtkCnhhyoWK3GJ9zHvxI5UEHQz6jkIMV8x1eyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/ck2fau22ee-61n19gt1b8.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jefwejb9ld", "Integrity": "+/824NtfTA0L7Mr42Fo+86YJP4R5cigdi9S7Fx5VVl4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/ctbf2xrrum-46ein0sx1k.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tjkh9ole7z", "Integrity": "t6QWSwy3Hj2vujZrQx105pCheaKB1HxwaYvEfAj2ZQQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/cv2idzmv24-kbrnm935zg.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gs9lvivu8f", "Integrity": "0JvrXl3Fun1tyY8CGbKufhGRvhas6A+KnWWHAP5nHDA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/cxlmau4v5s-0i3buxo5is.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7tens4czd6", "Integrity": "u1a19QoSpVBP/3/GNlsnGb5GykPh2UehRIZmQppbtaw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/dr38shwxjt-k8d9w2qqmf.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h4n0k4tuhh", "Integrity": "7xavgsBS8xT/BN/R5lSx9RiFyWsBl9QnQLDIZ7zoFDM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/edc5guiudl-fsbi9cje9m.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "074iv4flnu", "Integrity": "vCT+VlfhQBUpS2HBKOH8r5ZoklQw4DVpGqswUUUN/3o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/et2vzgdp38-xtxxf3hu2r.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/js/site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pmnrhqz0gl", "Integrity": "YTkfXSHFEnZ6310TKacrOsSM7+9iBIEpAwW6DhmB2Rg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/js/site.js", "FileLength": 189, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/f94tkdconl-npsjqeddi6.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "css/site#[.{fingerprint=npsjqeddi6}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/css/site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "obmgelu352", "Integrity": "/EjSu2H6L0O1u1jhQRoNhsK2w1YbSoT/GdbPHsUCdHU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/css/site.css", "FileLength": 347, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/fbxag7xoof-c2jlpeoesf.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2tsi8wjm49", "Integrity": "qmUx2B7T8Z7IyR6EmiZHJbZ+Uv3DJEHJ7ssn+kdW1i8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/gkx5ezab59-06098lyss8.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f99l17lfqo", "Integrity": "I08/qzqZLIAEpYn7Q8CYb1D16mMIgck4FCEsuf/k2qA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/gpo7e5xb47-ag7o75518u.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2nuylusgcd", "Integrity": "YgOEl7ge6Ki6+76DGHj7yoX9Qs+JWj09WvIFSjhk2cs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/gye3uq2aze-h1s4sie4z3.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "50wtf4x982", "Integrity": "u2mOk/RK572xHpx+p8mZVoEsMjVVs6FWDy+64B1PiEg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/h8lt1guizi-jj8uyg4cgr.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d8y5vl9a47", "Integrity": "jT9UAw765SpUas395Rv9uCfj03Yr+71SED9cFddplgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/hr6m1t3wha-ausgxo2sd3.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x7xhrh30on", "Integrity": "bdHm4sBDLNpP67uFuXfCYaN9yiCl0fweKmoFyHzKAKU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/hzgw3rrxnr-nvvlpmu67g.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sjj8j<PERSON><PERSON><PERSON>", "Integrity": "CKDg86xi9pW/IY+jBKojCJ2lCb9fghS+r0GO3BA0/uk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/jh4hvided3-bqjiyaj88i.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i6o6rzi6e5", "Integrity": "6sbTVG9CbhFMDZwUY53RzdkwigZG79kLFgwyS1Glsik=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/jycomgcr9u-pj5nd1wqec.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hwn1wvut14", "Integrity": "QUuhsynxQOlfK5DQYL8JUQrTx7RfwGegz12iNenpec4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/kh3yuzknfi-mrlpezrjn3.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kylvg5djs1", "Integrity": "7FTIZ9Sy+RoTM5k0vROIfrcx1T2djqzEhoWUTyEGYdk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/lbaey89jvq-cosvhxvwiu.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ysv1bhacdl", "Integrity": "vjltiawUeinKvizAJ7M/caKNxLqW7ps84r4WfEONYm8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/nah7djua8u-dxx9fxp4il.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lgyuo7x0s4", "Integrity": "C1IbpiR8c+OS9hwOyyc8ailOEn1kYdVwulZnJBLUZUE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/nmmlxnq2n6-r4e9w2rdcm.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yftpy5cpfg", "Integrity": "sgi5Kepkl3Dg2bCvP8dDG05m2De435WzMamvkQXqbC8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/nwxrrfr11z-v0zj4ognzu.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mytyes4x0w", "Integrity": "0vUD4yU9EsbYFJN7o3MTz6YEEuzR2rBTCsiEfim4MMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/okhcuobryv-lzl9nlhx6b.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/dist/jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "16x7cx6d2t", "Integrity": "qfPxa9GDxZoylcELsgG9gebdHGdnkzq24NR+WZjDU4w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/dist/jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/otjio8kepc-6cfz1n2cew.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jpb775kzpb", "Integrity": "+S1dgJktEranDVAQPXUu9kuAGDv1gLsHGc8IIKVtqYc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/p1y9zxb3a5-0j3bgjxly4.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c31in2ygw4", "Integrity": "+Eq85CUhXtCIudgWXb2bTNfSCy1lIH6+A8wRel8gj8o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/pj4gksa76a-erw9l3u2r3.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r5hrm1lmpo", "Integrity": "n9lwM63nmSEO58y5cFId0ei5O8p9sUBg8gTm7r7eYVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/pvz10cqcrd-ub07r2b239.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8hpebz51hn", "Integrity": "fF8POkEkJldavk6wKUDw53eThz5r51vhHgFLWoKMdms=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/pw8pr2kp2i-ttgo8qnofa.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jzt1faf8c9", "Integrity": "8epPvrp0xBXMxP0dnv0Axc8mOq0ExnxVGCD6PJLwD7k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/q9s2mgx7op-2z0ns9nrw6.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rd6158ugq2", "Integrity": "j/v/J/tax404y6srAmEl0GjGbqMVN3aVVGkR3t0Jexw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/qq7vsmffi6-notf2xhcfb.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x46mtph5rm", "Integrity": "kVm75Syt0B86ikuL4igEkX315p71WyMLSh+YvGyq2ZY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/r93uqjk6un-jd9uben2k1.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "medyw2v0g8", "Integrity": "XtA0iscOHJ5VhOGbMSuA+/EtbSO9eGu3paD3xmISDCk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/syw4rzjclx-pk9g2wxc8p.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3seejrgs75", "Integrity": "U4Fb0kGgh99rOTlgKWLxJ9830Vhug5WC4AtpIoTow/A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/uwnbtgywmj-mlv21k5csn.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1qozky2jjc", "Integrity": "F1wMlgb/juW08DFUssW6Pm9EBKfjXXRiSK0F5FgXxJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/vkb8dl48wp-493y06b0oq.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cyi8aijtl7", "Integrity": "DKxtpOFRkjSEaPf7hRuWjmLIaLKqWeH4MMNTVQusPtg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/w1qcg7ny2i-muycvpuwrr.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o86z8u5yli", "Integrity": "WBpiN7hQEKwBhPHtE1Y1X30ghH9XVaxvnIrfAk0S0nM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/w4cj460fyr-tdbxkamptv.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmro2n373j", "Integrity": "4o8ZFL3Xy745xrgVGJ9bpY7hVbrk9B+fSaOrYfwE0EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/x12zmy9w2f-vr1egmr9el.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3qrra9blz7", "Integrity": "5Twc4TJNTMb/v4Us4GQzp7G2D/Azi6nb8XdxLCPc8A0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/x4fl886j3g-83jwlth58m.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/dist/additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0e0do7h7ay", "Integrity": "OKcAH26EdtYnFJnGz+MbWB/hAFCDtD3z03cV7nsNxlg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/dist/additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/yj32osumsz-khv3u5hwcm.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8e2m53j2ko", "Integrity": "oHauap4vmOF6HuinJ92NQBS78CUi6AO0VaPm8ukXXNo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/yppz3hmjub-s35ty4nyc5.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lzu6cjurhc", "Integrity": "n8W3VdC4I3veW4VnsbrnEGvARFEgXX9cBa7M89gRvWI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/yyxqjkh2b6-ee0r1s7dh0.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "it9bluv7eg", "Integrity": "diO2QLE0yMAAew7HQTblCm4XSnn3B/UCayHN9rHzWCs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/z4zkotwuf3-y7v9cxd14o.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hwhgmdus79", "Integrity": "XzeYhXaQJrHdbijtyDQZA554STHRsDLunRk6rrp9uiA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/zfueim4itm-356vix0kms.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w02302tvku", "Integrity": "APtdKLCc/nK1prhFh6ytj7bT4olz3PpIJvWoVVDWRGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/zmeu8j569y-iovd86k7lj.gz", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tmwl38fjzo", "Integrity": "dUvVCTDcbFI5khZmwMe5maCZAf7l+wbCXWpFM2MAcb0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/scopedcss/bundle/HSU-NguyenHoangThanhSang_22207613.styles.css", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Computed", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/scopedcss/bundle/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>hanhSang_22207613#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "4vt8tgilx7", "Integrity": "MCYvQDzTwdMUrZtyZ4qfLf00cZvOng+oMhSroxMpKLE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/scopedcss/bundle/HSU-NguyenHoangThanhSang_22207613.styles.css", "FileLength": 1102, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/scopedcss/projectbundle/HSU-NguyenHoangThanhSang_22207613.bundle.scp.css", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Computed", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/scopedcss/projectbundle/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>g<PERSON>hanhSang_22207613#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "4vt8tgilx7", "Integrity": "MCYvQDzTwdMUrZtyZ4qfLf00cZvOng+oMhSroxMpKLE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/scopedcss/projectbundle/HSU-NguyenHoangThanhSang_22207613.bundle.scp.css", "FileLength": 1102, "LastWriteTime": "2025-07-21T13:02:10+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/css/site.css", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "npsjqeddi6", "Integrity": "5YUbnxS8X4ThbhTYL7c/6kIunZ3rlN4dFM+4PhdEHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/css/site.css", "FileLength": 730, "LastWriteTime": "2025-07-19T12:59:51+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/favicon.ico", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/js/site.js", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/js/site.js", "FileLength": 231, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.css", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.js", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/LICENSE", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/dist/additional-methods.js", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/dist/jquery.validate.js", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/LICENSE.md", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.js", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.js", "FileLength": 285314, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.min.js", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.min.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.slim.js", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.slim.min.js", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.slim.min.map", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/LICENSE.txt", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-19T06:38:35+00:00"}, {"Identity": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/uploads/products/3dd3392a-ee7a-4ea0-93b6-271ce8cfbba9_prod-4.png", "SourceId": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/", "BasePath": "_content/HSU-Ng<PERSON>enHoangThanhSang_22207613", "RelativePath": "uploads/products/3dd3392a-ee7a-4ea0-93b6-271ce8cfbba9_prod-4#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sxlfuk6pbp", "Integrity": "MJUREZfhPS3ojZlF6uuGRDqZyixXr/8RDQ/tHL1uDjI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/uploads/products/3dd3392a-ee7a-4ea0-93b6-271ce8cfbba9_prod-4.png", "FileLength": 226458, "LastWriteTime": "2025-07-19T08:26:38+00:00"}], "Endpoints": [{"Route": "css/site.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/f94tkdconl-npsjqeddi6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002873563218"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "347"}, {"Name": "ETag", "Value": "\"/EjSu2H6L0O1u1jhQRoNhsK2w1YbSoT/GdbPHsUCdHU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"5YUbnxS8X4ThbhTYL7c/6kIunZ3rlN4dFM+4PhdEHGw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5YUbnxS8X4ThbhTYL7c/6kIunZ3rlN4dFM+4PhdEHGw="}]}, {"Route": "css/site.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "730"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5YUbnxS8X4ThbhTYL7c/6kIunZ3rlN4dFM+4PhdEHGw=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 12:59:51 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5YUbnxS8X4ThbhTYL7c/6kIunZ3rlN4dFM+4PhdEHGw="}]}, {"Route": "css/site.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/f94tkdconl-npsjqeddi6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "347"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/EjSu2H6L0O1u1jhQRoNhsK2w1YbSoT/GdbPHsUCdHU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/EjSu2H6L0O1u1jhQRoNhsK2w1YbSoT/GdbPHsUCdHU="}]}, {"Route": "css/site.npsjqeddi6.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/f94tkdconl-npsjqeddi6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002873563218"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "347"}, {"Name": "ETag", "Value": "\"/EjSu2H6L0O1u1jhQRoNhsK2w1YbSoT/GdbPHsUCdHU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"5YUbnxS8X4ThbhTYL7c/6kIunZ3rlN4dFM+4PhdEHGw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "npsjqeddi6"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-5YUbnxS8X4ThbhTYL7c/6kIunZ3rlN4dFM+4PhdEHGw="}]}, {"Route": "css/site.npsjqeddi6.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "730"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5YUbnxS8X4ThbhTYL7c/6kIunZ3rlN4dFM+4PhdEHGw=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 12:59:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "npsjqeddi6"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-5YUbnxS8X4ThbhTYL7c/6kIunZ3rlN4dFM+4PhdEHGw="}]}, {"Route": "css/site.npsjqeddi6.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/f94tkdconl-npsjqeddi6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "347"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/EjSu2H6L0O1u1jhQRoNhsK2w1YbSoT/GdbPHsUCdHU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "npsjqeddi6"}, {"Name": "label", "Value": "css/site.css.gz"}, {"Name": "integrity", "Value": "sha256-/EjSu2H6L0O1u1jhQRoNhsK2w1YbSoT/GdbPHsUCdHU="}]}, {"Route": "favicon.61n19gt1b8.ico", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/ck2fau22ee-61n19gt1b8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000405022276"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2468"}, {"Name": "ETag", "Value": "\"+/824NtfTA0L7Mr42Fo+86YJP4R5cigdi9S7Fx5VVl4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "W/\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "favicon.61n19gt1b8.ico", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "favicon.61n19gt1b8.ico.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/ck2fau22ee-61n19gt1b8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2468"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"+/824NtfTA0L7Mr42Fo+86YJP4R5cigdi9S7Fx5VVl4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "label", "Value": "favicon.ico.gz"}, {"Name": "integrity", "Value": "sha256-+/824NtfTA0L7Mr42Fo+86YJP4R5cigdi9S7Fx5VVl4="}]}, {"Route": "favicon.ico", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/ck2fau22ee-61n19gt1b8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000405022276"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2468"}, {"Name": "ETag", "Value": "\"+/824NtfTA0L7Mr42Fo+86YJP4R5cigdi9S7Fx5VVl4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "W/\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "favicon.ico", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "favicon.ico.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/ck2fau22ee-61n19gt1b8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2468"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"+/824NtfTA0L7Mr42Fo+86YJP4R5cigdi9S7Fx5VVl4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+/824NtfTA0L7Mr42Fo+86YJP4R5cigdi9S7Fx5VVl4="}]}, {"Route": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613.4vt8tgilx7.bundle.scp.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/599ujbjjs2-4vt8tgilx7.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001792114695"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "557"}, {"Name": "ETag", "Value": "\"7Kno3CEt/4W+3JvR4jpoKmAwaV4vlM73YudkzfUnvzI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"MCYvQDzTwdMUrZtyZ4qfLf00cZvOng+oMhSroxMpKLE=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4vt8tgilx7"}, {"Name": "label", "Value": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-MCYvQDzTwdMUrZtyZ4qfLf00cZvOng+oMhSroxMpKLE="}]}, {"Route": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613.4vt8tgilx7.bundle.scp.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/scopedcss/projectbundle/HSU-NguyenHoangThanhSang_22207613.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1102"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"MCYvQDzTwdMUrZtyZ4qfLf00cZvOng+oMhSroxMpKLE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4vt8tgilx7"}, {"Name": "label", "Value": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-MCYvQDzTwdMUrZtyZ4qfLf00cZvOng+oMhSroxMpKLE="}]}, {"Route": "HSU-<PERSON><PERSON><PERSON><PERSON>oangThanhSang_22207613.4vt8tgilx7.bundle.scp.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/599ujbjjs2-4vt8tgilx7.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "557"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7Kno3CEt/4W+3JvR4jpoKmAwaV4vlM73YudkzfUnvzI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4vt8tgilx7"}, {"Name": "label", "Value": "HSU-Ng<PERSON><PERSON><PERSON>oangThanhSang_22207613.bundle.scp.css.gz"}, {"Name": "integrity", "Value": "sha256-7Kno3CEt/4W+3JvR4jpoKmAwaV4vlM73YudkzfUnvzI="}]}, {"Route": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>h<PERSON>ang_22207613.4vt8tgilx7.styles.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/3gtazo3ge3-4vt8tgilx7.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001792114695"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "557"}, {"Name": "ETag", "Value": "\"7Kno3CEt/4W+3JvR4jpoKmAwaV4vlM73YudkzfUnvzI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"MCYvQDzTwdMUrZtyZ4qfLf00cZvOng+oMhSroxMpKLE=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4vt8tgilx7"}, {"Name": "label", "Value": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>h<PERSON>ang_22207613.styles.css"}, {"Name": "integrity", "Value": "sha256-MCYvQDzTwdMUrZtyZ4qfLf00cZvOng+oMhSroxMpKLE="}]}, {"Route": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>h<PERSON>ang_22207613.4vt8tgilx7.styles.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/scopedcss/bundle/HSU-NguyenHoangThanhSang_22207613.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1102"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"MCYvQDzTwdMUrZtyZ4qfLf00cZvOng+oMhSroxMpKLE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4vt8tgilx7"}, {"Name": "label", "Value": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>h<PERSON>ang_22207613.styles.css"}, {"Name": "integrity", "Value": "sha256-MCYvQDzTwdMUrZtyZ4qfLf00cZvOng+oMhSroxMpKLE="}]}, {"Route": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>g<PERSON>hanhSang_22207613.4vt8tgilx7.styles.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/3gtazo3ge3-4vt8tgilx7.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "557"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7Kno3CEt/4W+3JvR4jpoKmAwaV4vlM73YudkzfUnvzI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4vt8tgilx7"}, {"Name": "label", "Value": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>g<PERSON>hanhSang_22207613.styles.css.gz"}, {"Name": "integrity", "Value": "sha256-7Kno3CEt/4W+3JvR4jpoKmAwaV4vlM73YudkzfUnvzI="}]}, {"Route": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613.bundle.scp.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/599ujbjjs2-4vt8tgilx7.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001792114695"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "557"}, {"Name": "ETag", "Value": "\"7Kno3CEt/4W+3JvR4jpoKmAwaV4vlM73YudkzfUnvzI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"MCYvQDzTwdMUrZtyZ4qfLf00cZvOng+oMhSroxMpKLE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MCYvQDzTwdMUrZtyZ4qfLf00cZvOng+oMhSroxMpKLE="}]}, {"Route": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613.bundle.scp.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/scopedcss/projectbundle/HSU-NguyenHoangThanhSang_22207613.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1102"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"MCYvQDzTwdMUrZtyZ4qfLf00cZvOng+oMhSroxMpKLE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MCYvQDzTwdMUrZtyZ4qfLf00cZvOng+oMhSroxMpKLE="}]}, {"Route": "HSU-Ng<PERSON><PERSON><PERSON>oangThanhSang_22207613.bundle.scp.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/599ujbjjs2-4vt8tgilx7.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "557"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7Kno3CEt/4W+3JvR4jpoKmAwaV4vlM73YudkzfUnvzI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7Kno3CEt/4W+3JvR4jpoKmAwaV4vlM73YudkzfUnvzI="}]}, {"Route": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>h<PERSON>ang_22207613.styles.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/3gtazo3ge3-4vt8tgilx7.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001792114695"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "557"}, {"Name": "ETag", "Value": "\"7Kno3CEt/4W+3JvR4jpoKmAwaV4vlM73YudkzfUnvzI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"MCYvQDzTwdMUrZtyZ4qfLf00cZvOng+oMhSroxMpKLE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MCYvQDzTwdMUrZtyZ4qfLf00cZvOng+oMhSroxMpKLE="}]}, {"Route": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>h<PERSON>ang_22207613.styles.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/scopedcss/bundle/HSU-NguyenHoangThanhSang_22207613.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1102"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"MCYvQDzTwdMUrZtyZ4qfLf00cZvOng+oMhSroxMpKLE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MCYvQDzTwdMUrZtyZ4qfLf00cZvOng+oMhSroxMpKLE="}]}, {"Route": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>g<PERSON>hanhSang_22207613.styles.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/3gtazo3ge3-4vt8tgilx7.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "557"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7Kno3CEt/4W+3JvR4jpoKmAwaV4vlM73YudkzfUnvzI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7Kno3CEt/4W+3JvR4jpoKmAwaV4vlM73YudkzfUnvzI="}]}, {"Route": "js/site.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/et2vzgdp38-xtxxf3hu2r.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.005263157895"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "189"}, {"Name": "ETag", "Value": "\"YTkfXSHFEnZ6310TKacrOsSM7+9iBIEpAwW6DhmB2Rg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo="}]}, {"Route": "js/site.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "231"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo="}]}, {"Route": "js/site.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/et2vzgdp38-xtxxf3hu2r.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "189"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YTkfXSHFEnZ6310TKacrOsSM7+9iBIEpAwW6DhmB2Rg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YTkfXSHFEnZ6310TKacrOsSM7+9iBIEpAwW6DhmB2Rg="}]}, {"Route": "js/site.xtxxf3hu2r.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/et2vzgdp38-xtxxf3hu2r.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.005263157895"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "189"}, {"Name": "ETag", "Value": "\"YTkfXSHFEnZ6310TKacrOsSM7+9iBIEpAwW6DhmB2Rg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xtxxf3hu2r"}, {"Name": "label", "Value": "js/site.js"}, {"Name": "integrity", "Value": "sha256-hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo="}]}, {"Route": "js/site.xtxxf3hu2r.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "231"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xtxxf3hu2r"}, {"Name": "label", "Value": "js/site.js"}, {"Name": "integrity", "Value": "sha256-hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo="}]}, {"Route": "js/site.xtxxf3hu2r.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/et2vzgdp38-xtxxf3hu2r.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "189"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YTkfXSHFEnZ6310TKacrOsSM7+9iBIEpAwW6DhmB2Rg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xtxxf3hu2r"}, {"Name": "label", "Value": "js/site.js.gz"}, {"Name": "integrity", "Value": "sha256-YTkfXSHFEnZ6310TKacrOsSM7+9iBIEpAwW6DhmB2Rg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.bqjiyaj88i.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/jh4hvided3-bqjiyaj88i.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148235992"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6745"}, {"Name": "ETag", "Value": "\"6sbTVG9CbhFMDZwUY53RzdkwigZG79kLFgwyS1Glsik=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bqjiyaj88i"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css"}, {"Name": "integrity", "Value": "sha256-Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.bqjiyaj88i.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70329"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bqjiyaj88i"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css"}, {"Name": "integrity", "Value": "sha256-Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.bqjiyaj88i.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/jh4hvided3-bqjiyaj88i.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6745"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"6sbTVG9CbhFMDZwUY53RzdkwigZG79kLFgwyS1Glsik=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bqjiyaj88i"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.gz"}, {"Name": "integrity", "Value": "sha256-6sbTVG9CbhFMDZwUY53RzdkwigZG79kLFgwyS1Glsik="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/jh4hvided3-bqjiyaj88i.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148235992"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6745"}, {"Name": "ETag", "Value": "\"6sbTVG9CbhFMDZwUY53RzdkwigZG79kLFgwyS1Glsik=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70329"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/fbxag7xoof-c2jlpeoesf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030492453"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32794"}, {"Name": "ETag", "Value": "\"qmUx2B7T8Z7IyR6EmiZHJbZ+Uv3DJEHJ7ssn+kdW1i8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2j<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}, {"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "203221"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2j<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}, {"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/fbxag7xoof-c2jlpeoesf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32794"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"qmUx2B7T8Z7IyR6EmiZHJbZ+Uv3DJEHJ7ssn+kdW1i8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2j<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz"}, {"Name": "integrity", "Value": "sha256-qmUx2B7T8Z7IyR6EmiZHJbZ+Uv3DJEHJ7ssn+kdW1i8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/jh4hvided3-bqjiyaj88i.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6745"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"6sbTVG9CbhFMDZwUY53RzdkwigZG79kLFgwyS1Glsik=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6sbTVG9CbhFMDZwUY53RzdkwigZG79kLFgwyS1Glsik="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/fbxag7xoof-c2jlpeoesf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030492453"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32794"}, {"Name": "ETag", "Value": "\"qmUx2B7T8Z7IyR6EmiZHJbZ+Uv3DJEHJ7ssn+kdW1i8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "203221"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/fbxag7xoof-c2jlpeoesf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32794"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"qmUx2B7T8Z7IyR6EmiZHJbZ+Uv3DJEHJ7ssn+kdW1i8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qmUx2B7T8Z7IyR6EmiZHJbZ+Uv3DJEHJ7ssn+kdW1i8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/pj4gksa76a-erw9l3u2r3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167504188"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5969"}, {"Name": "ETag", "Value": "\"n9lwM63nmSEO58y5cFId0ei5O8p9sUBg8gTm7r7eYVo=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51795"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/06lgtu8qye-aexeepp0ev.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072421784"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13807"}, {"Name": "ETag", "Value": "\"UFok8WEocoom8UMWS+es3N9UMNaqVZdnOD5Mbxj/F/s=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aexeepp0ev"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, {"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "115986"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aexeepp0ev"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, {"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/06lgtu8qye-aexeepp0ev.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"UFok8WEocoom8UMWS+es3N9UMNaqVZdnOD5Mbxj/F/s=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aexeepp0ev"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-UFok8WEocoom8UMWS+es3N9UMNaqVZdnOD5Mbxj/F/s="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/pj4gksa76a-erw9l3u2r3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5969"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"n9lwM63nmSEO58y5cFId0ei5O8p9sUBg8gTm7r7eYVo=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-n9lwM63nmSEO58y5cFId0ei5O8p9sUBg8gTm7r7eYVo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/06lgtu8qye-aexeepp0ev.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072421784"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13807"}, {"Name": "ETag", "Value": "\"UFok8WEocoom8UMWS+es3N9UMNaqVZdnOD5Mbxj/F/s=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "115986"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/06lgtu8qye-aexeepp0ev.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"UFok8WEocoom8UMWS+es3N9UMNaqVZdnOD5Mbxj/F/s=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UFok8WEocoom8UMWS+es3N9UMNaqVZdnOD5Mbxj/F/s="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.erw9l3u2r3.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/pj4gksa76a-erw9l3u2r3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167504188"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5969"}, {"Name": "ETag", "Value": "\"n9lwM63nmSEO58y5cFId0ei5O8p9sUBg8gTm7r7eYVo=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "erw9l3u2r3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}, {"Name": "integrity", "Value": "sha256-5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.erw9l3u2r3.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51795"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "erw9l3u2r3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}, {"Name": "integrity", "Value": "sha256-5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.erw9l3u2r3.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/pj4gksa76a-erw9l3u2r3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5969"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"n9lwM63nmSEO58y5cFId0ei5O8p9sUBg8gTm7r7eYVo=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "erw9l3u2r3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz"}, {"Name": "integrity", "Value": "sha256-n9lwM63nmSEO58y5cFId0ei5O8p9sUBg8gTm7r7eYVo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/0mlfmn3zcy-d7shbmvgxk.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148148148"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6749"}, {"Name": "ETag", "Value": "\"K/ZIAouXSbIcQFaQIVnwVXQB7VwD/k3K0f1FLrpT1DA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70403"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/hr6m1t3wha-ausgxo2sd3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030493383"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32793"}, {"Name": "ETag", "Value": "\"bdHm4sBDLNpP67uFuXfCYaN9yiCl0fweKmoFyHzKAKU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausgxo2sd3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "203225"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausgxo2sd3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/hr6m1t3wha-ausgxo2sd3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32793"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"bdHm4sBDLNpP67uFuXfCYaN9yiCl0fweKmoFyHzKAKU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausgxo2sd3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz"}, {"Name": "integrity", "Value": "sha256-bdHm4sBDLNpP67uFuXfCYaN9yiCl0fweKmoFyHzKAKU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/0mlfmn3zcy-d7shbmvgxk.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6749"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K/ZIAouXSbIcQFaQIVnwVXQB7VwD/k3K0f1FLrpT1DA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K/ZIAouXSbIcQFaQIVnwVXQB7VwD/k3K0f1FLrpT1DA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/hr6m1t3wha-ausgxo2sd3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030493383"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32793"}, {"Name": "ETag", "Value": "\"bdHm4sBDLNpP67uFuXfCYaN9yiCl0fweKmoFyHzKAKU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "203225"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/hr6m1t3wha-ausgxo2sd3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32793"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"bdHm4sBDLNpP67uFuXfCYaN9yiCl0fweKmoFyHzKAKU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bdHm4sBDLNpP67uFuXfCYaN9yiCl0fweKmoFyHzKAKU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.d7shbmvgxk.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/0mlfmn3zcy-d7shbmvgxk.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148148148"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6749"}, {"Name": "ETag", "Value": "\"K/ZIAouXSbIcQFaQIVnwVXQB7VwD/k3K0f1FLrpT1DA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d7shbmvgxk"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}, {"Name": "integrity", "Value": "sha256-CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.d7shbmvgxk.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70403"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d7shbmvgxk"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}, {"Name": "integrity", "Value": "sha256-CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.d7shbmvgxk.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/0mlfmn3zcy-d7shbmvgxk.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6749"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K/ZIAouXSbIcQFaQIVnwVXQB7VwD/k3K0f1FLrpT1DA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d7shbmvgxk"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz"}, {"Name": "integrity", "Value": "sha256-K/ZIAouXSbIcQFaQIVnwVXQB7VwD/k3K0f1FLrpT1DA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/dr38shwxjt-k8d9w2qqmf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167448091"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5971"}, {"Name": "ETag", "Value": "\"7xavgsBS8xT/BN/R5lSx9RiFyWsBl9QnQLDIZ7zoFDM=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51870"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/lbaey89jvq-cosvhxvwiu.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072379849"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13815"}, {"Name": "ETag", "Value": "\"vjltiawUeinKvizAJ7M/caKNxLqW7ps84r4WfEONYm8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cosvhxvwiu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "116063"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cosvhxvwiu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/lbaey89jvq-cosvhxvwiu.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13815"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"vjltiawUeinKvizAJ7M/caKNxLqW7ps84r4WfEONYm8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cosvhxvwiu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-vjltiawUeinKvizAJ7M/caKNxLqW7ps84r4WfEONYm8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/dr38shwxjt-k8d9w2qqmf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5971"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7xavgsBS8xT/BN/R5lSx9RiFyWsBl9QnQLDIZ7zoFDM=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7xavgsBS8xT/BN/R5lSx9RiFyWsBl9QnQLDIZ7zoFDM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/lbaey89jvq-cosvhxvwiu.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072379849"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13815"}, {"Name": "ETag", "Value": "\"vjltiawUeinKvizAJ7M/caKNxLqW7ps84r4WfEONYm8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "116063"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/lbaey89jvq-cosvhxvwiu.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13815"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"vjltiawUeinKvizAJ7M/caKNxLqW7ps84r4WfEONYm8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vjltiawUeinKvizAJ7M/caKNxLqW7ps84r4WfEONYm8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.k8d9w2qqmf.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/dr38shwxjt-k8d9w2qqmf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167448091"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5971"}, {"Name": "ETag", "Value": "\"7xavgsBS8xT/BN/R5lSx9RiFyWsBl9QnQLDIZ7zoFDM=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k8d9w2qqmf"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.k8d9w2qqmf.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51870"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k8d9w2qqmf"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.k8d9w2qqmf.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/dr38shwxjt-k8d9w2qqmf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5971"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7xavgsBS8xT/BN/R5lSx9RiFyWsBl9QnQLDIZ7zoFDM=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k8d9w2qqmf"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz"}, {"Name": "integrity", "Value": "sha256-7xavgsBS8xT/BN/R5lSx9RiFyWsBl9QnQLDIZ7zoFDM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/pvz10cqcrd-ub07r2b239.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000295770482"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3380"}, {"Name": "ETag", "Value": "\"fF8POkEkJldavk6wKUDw53eThz5r51vhHgFLWoKMdms=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12065"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/8k3s542c9c-fvhpjtyr6v.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038726667"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25821"}, {"Name": "ETag", "Value": "\"xaCEzyQKiV85d/qRa4kxMNFkjAE8f+xqfcs47A+bU98=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvhpjtyr6v"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, {"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "129371"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvhpjtyr6v"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, {"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/8k3s542c9c-fvhpjtyr6v.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25821"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xaCEzyQKiV85d/qRa4kxMNFkjAE8f+xqfcs47A+bU98=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvhpjtyr6v"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz"}, {"Name": "integrity", "Value": "sha256-xaCEzyQKiV85d/qRa4kxMNFkjAE8f+xqfcs47A+bU98="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/pvz10cqcrd-ub07r2b239.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3380"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fF8POkEkJldavk6wKUDw53eThz5r51vhHgFLWoKMdms=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fF8POkEkJldavk6wKUDw53eThz5r51vhHgFLWoKMdms="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/8k3s542c9c-fvhpjtyr6v.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038726667"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25821"}, {"Name": "ETag", "Value": "\"xaCEzyQKiV85d/qRa4kxMNFkjAE8f+xqfcs47A+bU98=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "129371"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/8k3s542c9c-fvhpjtyr6v.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25821"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xaCEzyQKiV85d/qRa4kxMNFkjAE8f+xqfcs47A+bU98=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xaCEzyQKiV85d/qRa4kxMNFkjAE8f+xqfcs47A+bU98="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.b7pk76d08c.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/1azoxyujlq-b7pk76d08c.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000311138768"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3213"}, {"Name": "ETag", "Value": "\"fAr5V6ldLYzgCQCrnJo6Cu7m2UEdDJE1pQ0wbhaBtUo=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b7pk76d08c"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, {"Name": "integrity", "Value": "sha256-l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.b7pk76d08c.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10126"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b7pk76d08c"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, {"Name": "integrity", "Value": "sha256-l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.b7pk76d08c.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/1azoxyujlq-b7pk76d08c.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3213"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fAr5V6ldLYzgCQCrnJo6Cu7m2UEdDJE1pQ0wbhaBtUo=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b7pk76d08c"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz"}, {"Name": "integrity", "Value": "sha256-fAr5V6ldLYzgCQCrnJo6Cu7m2UEdDJE1pQ0wbhaBtUo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/1azoxyujlq-b7pk76d08c.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000311138768"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3213"}, {"Name": "ETag", "Value": "\"fAr5V6ldLYzgCQCrnJo6Cu7m2UEdDJE1pQ0wbhaBtUo=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10126"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/edc5guiudl-fsbi9cje9m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000079440737"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12587"}, {"Name": "ETag", "Value": "\"vCT+VlfhQBUpS2HBKOH8r5ZoklQw4DVpGqswUUUN/3o=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fsbi9cje9m"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, {"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51369"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fsbi9cje9m"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, {"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/edc5guiudl-fsbi9cje9m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12587"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"vCT+VlfhQBUpS2HBKOH8r5ZoklQw4DVpGqswUUUN/3o=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fsbi9cje9m"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-vCT+VlfhQBUpS2HBKOH8r5ZoklQw4DVpGqswUUUN/3o="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/1azoxyujlq-b7pk76d08c.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3213"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fAr5V6ldLYzgCQCrnJo6Cu7m2UEdDJE1pQ0wbhaBtUo=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fAr5V6ldLYzgCQCrnJo6Cu7m2UEdDJE1pQ0wbhaBtUo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/edc5guiudl-fsbi9cje9m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000079440737"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12587"}, {"Name": "ETag", "Value": "\"vCT+VlfhQBUpS2HBKOH8r5ZoklQw4DVpGqswUUUN/3o=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51369"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/edc5guiudl-fsbi9cje9m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12587"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"vCT+VlfhQBUpS2HBKOH8r5ZoklQw4DVpGqswUUUN/3o=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vCT+VlfhQBUpS2HBKOH8r5ZoklQw4DVpGqswUUUN/3o="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/cgvb40ww4i-rzd6atqjts.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000296912114"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3367"}, {"Name": "ETag", "Value": "\"tTpkoOtkCnhhyoWK3GJ9zHvxI5UEHQz6jkIMV8x1eyE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12058"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/yyxqjkh2b6-ee0r1s7dh0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038708678"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25833"}, {"Name": "ETag", "Value": "\"diO2QLE0yMAAew7HQTblCm4XSnn3B/UCayHN9rHzWCs=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ee0r1s7dh0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "129386"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ee0r1s7dh0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/yyxqjkh2b6-ee0r1s7dh0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25833"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"diO2QLE0yMAAew7HQTblCm4XSnn3B/UCayHN9rHzWCs=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ee0r1s7dh0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz"}, {"Name": "integrity", "Value": "sha256-diO2QLE0yMAAew7HQTblCm4XSnn3B/UCayHN9rHzWCs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/cgvb40ww4i-rzd6atqjts.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3367"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tTpkoOtkCnhhyoWK3GJ9zHvxI5UEHQz6jkIMV8x1eyE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tTpkoOtkCnhhyoWK3GJ9zHvxI5UEHQz6jkIMV8x1eyE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/yyxqjkh2b6-ee0r1s7dh0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038708678"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25833"}, {"Name": "ETag", "Value": "\"diO2QLE0yMAAew7HQTblCm4XSnn3B/UCayHN9rHzWCs=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "129386"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/yyxqjkh2b6-ee0r1s7dh0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25833"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"diO2QLE0yMAAew7HQTblCm4XSnn3B/UCayHN9rHzWCs=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-diO2QLE0yMAAew7HQTblCm4XSnn3B/UCayHN9rHzWCs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/nah7djua8u-dxx9fxp4il.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000307976594"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3246"}, {"Name": "ETag", "Value": "\"C1IbpiR8c+OS9hwOyyc8ailOEn1kYdVwulZnJBLUZUE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10198"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/nah7djua8u-dxx9fxp4il.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3246"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"C1IbpiR8c+OS9hwOyyc8ailOEn1kYdVwulZnJBLUZUE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-C1IbpiR8c+OS9hwOyyc8ailOEn1kYdVwulZnJBLUZUE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/r93uqjk6un-jd9uben2k1.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000066423115"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15054"}, {"Name": "ETag", "Value": "\"XtA0iscOHJ5VhOGbMSuA+/EtbSO9eGu3paD3xmISDCk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jd9uben2k1"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "63943"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jd9uben2k1"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/r93uqjk6un-jd9uben2k1.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15054"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"XtA0iscOHJ5VhOGbMSuA+/EtbSO9eGu3paD3xmISDCk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jd9uben2k1"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-XtA0iscOHJ5VhOGbMSuA+/EtbSO9eGu3paD3xmISDCk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/r93uqjk6un-jd9uben2k1.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000066423115"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15054"}, {"Name": "ETag", "Value": "\"XtA0iscOHJ5VhOGbMSuA+/EtbSO9eGu3paD3xmISDCk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "63943"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/r93uqjk6un-jd9uben2k1.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15054"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"XtA0iscOHJ5VhOGbMSuA+/EtbSO9eGu3paD3xmISDCk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XtA0iscOHJ5VhOGbMSuA+/EtbSO9eGu3paD3xmISDCk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.dxx9fxp4il.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/nah7djua8u-dxx9fxp4il.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000307976594"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3246"}, {"Name": "ETag", "Value": "\"C1IbpiR8c+OS9hwOyyc8ailOEn1kYdVwulZnJBLUZUE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dxx9fxp4il"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.dxx9fxp4il.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10198"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dxx9fxp4il"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.dxx9fxp4il.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/nah7djua8u-dxx9fxp4il.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3246"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"C1IbpiR8c+OS9hwOyyc8ailOEn1kYdVwulZnJBLUZUE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dxx9fxp4il"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz"}, {"Name": "integrity", "Value": "sha256-C1IbpiR8c+OS9hwOyyc8ailOEn1kYdVwulZnJBLUZUE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.rzd6atqjts.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/cgvb40ww4i-rzd6atqjts.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000296912114"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3367"}, {"Name": "ETag", "Value": "\"tTpkoOtkCnhhyoWK3GJ9zHvxI5UEHQz6jkIMV8x1eyE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rzd6atqjts"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}, {"Name": "integrity", "Value": "sha256-V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.rzd6atqjts.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12058"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rzd6atqjts"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}, {"Name": "integrity", "Value": "sha256-V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.rzd6atqjts.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/cgvb40ww4i-rzd6atqjts.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3367"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tTpkoOtkCnhhyoWK3GJ9zHvxI5UEHQz6jkIMV8x1eyE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rzd6atqjts"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz"}, {"Name": "integrity", "Value": "sha256-tTpkoOtkCnhhyoWK3GJ9zHvxI5UEHQz6jkIMV8x1eyE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.ub07r2b239.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/pvz10cqcrd-ub07r2b239.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000295770482"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3380"}, {"Name": "ETag", "Value": "\"fF8POkEkJldavk6wKUDw53eThz5r51vhHgFLWoKMdms=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ub07r2b239"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css"}, {"Name": "integrity", "Value": "sha256-lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.ub07r2b239.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12065"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ub07r2b239"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css"}, {"Name": "integrity", "Value": "sha256-lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.ub07r2b239.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/pvz10cqcrd-ub07r2b239.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3380"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fF8POkEkJldavk6wKUDw53eThz5r51vhHgFLWoKMdms=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ub07r2b239"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz"}, {"Name": "integrity", "Value": "sha256-fF8POkEkJldavk6wKUDw53eThz5r51vhHgFLWoKMdms="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/yj32osumsz-khv3u5hwcm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083388926"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11991"}, {"Name": "ETag", "Value": "\"oHauap4vmOF6HuinJ92NQBS78CUi6AO0VaPm8ukXXNo=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107823"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/yj32osumsz-khv3u5hwcm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11991"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"oHauap4vmOF6HuinJ92NQBS78CUi6AO0VaPm8ukXXNo=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oHauap4vmOF6HuinJ92NQBS78CUi6AO0VaPm8ukXXNo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/nmmlxnq2n6-r4e9w2rdcm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022663403"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44123"}, {"Name": "ETag", "Value": "\"sgi5Kepkl3Dg2bCvP8dDG05m2De435WzMamvkQXqbC8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/nmmlxnq2n6-r4e9w2rdcm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"sgi5Kepkl3Dg2bCvP8dDG05m2De435WzMamvkQXqbC8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sgi5Kepkl3Dg2bCvP8dDG05m2De435WzMamvkQXqbC8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/nmmlxnq2n6-r4e9w2rdcm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022663403"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44123"}, {"Name": "ETag", "Value": "\"sgi5Kepkl3Dg2bCvP8dDG05m2De435WzMamvkQXqbC8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4e9w2rdcm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map"}, {"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4e9w2rdcm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map"}, {"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/nmmlxnq2n6-r4e9w2rdcm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"sgi5Kepkl3Dg2bCvP8dDG05m2De435WzMamvkQXqbC8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4e9w2rdcm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz"}, {"Name": "integrity", "Value": "sha256-sgi5Kepkl3Dg2bCvP8dDG05m2De435WzMamvkQXqbC8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.khv3u5hwcm.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/yj32osumsz-khv3u5hwcm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083388926"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11991"}, {"Name": "ETag", "Value": "\"oHauap4vmOF6HuinJ92NQBS78CUi6AO0VaPm8ukXXNo=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "khv3u5hwcm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css"}, {"Name": "integrity", "Value": "sha256-2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.khv3u5hwcm.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107823"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "khv3u5hwcm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css"}, {"Name": "integrity", "Value": "sha256-2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.khv3u5hwcm.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/yj32osumsz-khv3u5hwcm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11991"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"oHauap4vmOF6HuinJ92NQBS78CUi6AO0VaPm8ukXXNo=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "khv3u5hwcm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz"}, {"Name": "integrity", "Value": "sha256-oHauap4vmOF6HuinJ92NQBS78CUi6AO0VaPm8ukXXNo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/beh60p7esr-lcd1t2u6c8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090383225"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11063"}, {"Name": "ETag", "Value": "\"v/CvnuCD/wTHczgdFZP6S3KwG2qp/CQDVwOg5+Uuk30=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85352"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/6v6cpq9nz0-c2oey78nd0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041081259"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24341"}, {"Name": "ETag", "Value": "\"RQr8+RwL5iy9y9f4CBQTW/ZvhfqawQ+e/L2FVneMUeI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2oey78nd0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}, {"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180381"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2oey78nd0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}, {"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/6v6cpq9nz0-c2oey78nd0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24341"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"RQr8+RwL5iy9y9f4CBQTW/ZvhfqawQ+e/L2FVneMUeI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2oey78nd0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-RQr8+RwL5iy9y9f4CBQTW/ZvhfqawQ+e/L2FVneMUeI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/beh60p7esr-lcd1t2u6c8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11063"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"v/CvnuCD/wTHczgdFZP6S3KwG2qp/CQDVwOg5+Uuk30=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-v/CvnuCD/wTHczgdFZP6S3KwG2qp/CQDVwOg5+Uuk30="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/6v6cpq9nz0-c2oey78nd0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041081259"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24341"}, {"Name": "ETag", "Value": "\"RQr8+RwL5iy9y9f4CBQTW/ZvhfqawQ+e/L2FVneMUeI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180381"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/6v6cpq9nz0-c2oey78nd0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24341"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"RQr8+RwL5iy9y9f4CBQTW/ZvhfqawQ+e/L2FVneMUeI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RQr8+RwL5iy9y9f4CBQTW/ZvhfqawQ+e/L2FVneMUeI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.lcd1t2u6c8.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/beh60p7esr-lcd1t2u6c8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090383225"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11063"}, {"Name": "ETag", "Value": "\"v/CvnuCD/wTHczgdFZP6S3KwG2qp/CQDVwOg5+Uuk30=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lcd1t2u6c8"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css"}, {"Name": "integrity", "Value": "sha256-KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.lcd1t2u6c8.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85352"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lcd1t2u6c8"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css"}, {"Name": "integrity", "Value": "sha256-KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.lcd1t2u6c8.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/beh60p7esr-lcd1t2u6c8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11063"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"v/CvnuCD/wTHczgdFZP6S3KwG2qp/CQDVwOg5+Uuk30=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lcd1t2u6c8"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz"}, {"Name": "integrity", "Value": "sha256-v/CvnuCD/wTHczgdFZP6S3KwG2qp/CQDVwOg5+Uuk30="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/w4cj460fyr-tdbxkamptv.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083794201"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11933"}, {"Name": "ETag", "Value": "\"4o8ZFL3Xy745xrgVGJ9bpY7hVbrk9B+fSaOrYfwE0EM=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107691"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/w4cj460fyr-tdbxkamptv.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11933"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4o8ZFL3Xy745xrgVGJ9bpY7hVbrk9B+fSaOrYfwE0EM=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4o8ZFL3Xy745xrgVGJ9bpY7hVbrk9B+fSaOrYfwE0EM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/ad1sftlax3-j5mq2jizvt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022677794"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44095"}, {"Name": "ETag", "Value": "\"i93LDQ234gPIkUQkIc0/9sJEzqXyf8EBOksfgYR30V4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5mq2jizvt"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267476"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5mq2jizvt"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/ad1sftlax3-j5mq2jizvt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44095"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"i93LDQ234gPIkUQkIc0/9sJEzqXyf8EBOksfgYR30V4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5mq2jizvt"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz"}, {"Name": "integrity", "Value": "sha256-i93LDQ234gPIkUQkIc0/9sJEzqXyf8EBOksfgYR30V4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/ad1sftlax3-j5mq2jizvt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022677794"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44095"}, {"Name": "ETag", "Value": "\"i93LDQ234gPIkUQkIc0/9sJEzqXyf8EBOksfgYR30V4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267476"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/ad1sftlax3-j5mq2jizvt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44095"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"i93LDQ234gPIkUQkIc0/9sJEzqXyf8EBOksfgYR30V4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-i93LDQ234gPIkUQkIc0/9sJEzqXyf8EBOksfgYR30V4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.06098lyss8.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/gkx5ezab59-06098lyss8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090522314"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11046"}, {"Name": "ETag", "Value": "\"I08/qzqZLIAEpYn7Q8CYb1D16mMIgck4FCEsuf/k2qA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "06098lyss8"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.06098lyss8.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85281"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "06098lyss8"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.06098lyss8.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/gkx5ezab59-06098lyss8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11046"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"I08/qzqZLIAEpYn7Q8CYb1D16mMIgck4FCEsuf/k2qA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "06098lyss8"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz"}, {"Name": "integrity", "Value": "sha256-I08/qzqZLIAEpYn7Q8CYb1D16mMIgck4FCEsuf/k2qA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/gkx5ezab59-06098lyss8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090522314"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11046"}, {"Name": "ETag", "Value": "\"I08/qzqZLIAEpYn7Q8CYb1D16mMIgck4FCEsuf/k2qA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85281"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/gkx5ezab59-06098lyss8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11046"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"I08/qzqZLIAEpYn7Q8CYb1D16mMIgck4FCEsuf/k2qA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-I08/qzqZLIAEpYn7Q8CYb1D16mMIgck4FCEsuf/k2qA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/hzgw3rrxnr-nvvlpmu67g.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041162427"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24293"}, {"Name": "ETag", "Value": "\"CKDg86xi9pW/IY+jBKojCJ2lCb9fghS+r0GO3BA0/uk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180217"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/hzgw3rrxnr-nvvlpmu67g.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24293"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"CKDg86xi9pW/IY+jBKojCJ2lCb9fghS+r0GO3BA0/uk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CKDg86xi9pW/IY+jBKojCJ2lCb9fghS+r0GO3BA0/uk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/hzgw3rrxnr-nvvlpmu67g.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041162427"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24293"}, {"Name": "ETag", "Value": "\"CKDg86xi9pW/IY+jBKojCJ2lCb9fghS+r0GO3BA0/uk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nvvlpmu67g"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180217"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nvvlpmu67g"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/hzgw3rrxnr-nvvlpmu67g.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24293"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"CKDg86xi9pW/IY+jBKojCJ2lCb9fghS+r0GO3BA0/uk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nvvlpmu67g"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-CKDg86xi9pW/IY+jBKojCJ2lCb9fghS+r0GO3BA0/uk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.tdbxkamptv.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/w4cj460fyr-tdbxkamptv.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083794201"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11933"}, {"Name": "ETag", "Value": "\"4o8ZFL3Xy745xrgVGJ9bpY7hVbrk9B+fSaOrYfwE0EM=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tdbxkamptv"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}, {"Name": "integrity", "Value": "sha256-H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.tdbxkamptv.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107691"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tdbxkamptv"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}, {"Name": "integrity", "Value": "sha256-H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.tdbxkamptv.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/w4cj460fyr-tdbxkamptv.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11933"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4o8ZFL3Xy745xrgVGJ9bpY7hVbrk9B+fSaOrYfwE0EM=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tdbxkamptv"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz"}, {"Name": "integrity", "Value": "sha256-4o8ZFL3Xy745xrgVGJ9bpY7hVbrk9B+fSaOrYfwE0EM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/yppz3hmjub-s35ty4nyc5.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030073379"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33251"}, {"Name": "ETag", "Value": "\"n8W3VdC4I3veW4VnsbrnEGvARFEgXX9cBa7M89gRvWI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "281046"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/yppz3hmjub-s35ty4nyc5.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33251"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"n8W3VdC4I3veW4VnsbrnEGvARFEgXX9cBa7M89gRvWI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-n8W3VdC4I3veW4VnsbrnEGvARFEgXX9cBa7M89gRvWI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/jycomgcr9u-pj5nd1wqec.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008694896"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "115009"}, {"Name": "ETag", "Value": "\"QUuhsynxQOlfK5DQYL8JUQrTx7RfwGegz12iNenpec4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "679755"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/jycomgcr9u-pj5nd1wqec.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "115009"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"QUuhsynxQOlfK5DQYL8JUQrTx7RfwGegz12iNenpec4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QUuhsynxQOlfK5DQYL8JUQrTx7RfwGegz12iNenpec4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.pj5nd1wqec.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/jycomgcr9u-pj5nd1wqec.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008694896"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "115009"}, {"Name": "ETag", "Value": "\"QUuhsynxQOlfK5DQYL8JUQrTx7RfwGegz12iNenpec4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pj5nd1wqec"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map"}, {"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.pj5nd1wqec.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "679755"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pj5nd1wqec"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map"}, {"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.pj5nd1wqec.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/jycomgcr9u-pj5nd1wqec.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "115009"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"QUuhsynxQOlfK5DQYL8JUQrTx7RfwGegz12iNenpec4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pj5nd1wqec"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map.gz"}, {"Name": "integrity", "Value": "sha256-QUuhsynxQOlfK5DQYL8JUQrTx7RfwGegz12iNenpec4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.46ein0sx1k.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/ctbf2xrrum-46ein0sx1k.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032295569"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30963"}, {"Name": "ETag", "Value": "\"t6QWSwy3Hj2vujZrQx105pCheaKB1HxwaYvEfAj2ZQQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "46ein0sx1k"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.46ein0sx1k.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232803"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "46ein0sx1k"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.46ein0sx1k.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/ctbf2xrrum-46ein0sx1k.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30963"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"t6QWSwy3Hj2vujZrQx105pCheaKB1HxwaYvEfAj2ZQQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "46ein0sx1k"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.gz"}, {"Name": "integrity", "Value": "sha256-t6QWSwy3Hj2vujZrQx105pCheaKB1HxwaYvEfAj2ZQQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/ctbf2xrrum-46ein0sx1k.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032295569"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30963"}, {"Name": "ETag", "Value": "\"t6QWSwy3Hj2vujZrQx105pCheaKB1HxwaYvEfAj2ZQQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232803"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/ctbf2xrrum-46ein0sx1k.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30963"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"t6QWSwy3Hj2vujZrQx105pCheaKB1HxwaYvEfAj2ZQQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-t6QWSwy3Hj2vujZrQx105pCheaKB1HxwaYvEfAj2ZQQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/nwxrrfr11z-v0zj4ognzu.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010892297"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91807"}, {"Name": "ETag", "Value": "\"0vUD4yU9EsbYFJN7o3MTz6YEEuzR2rBTCsiEfim4MMk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "589892"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/nwxrrfr11z-v0zj4ognzu.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0vUD4yU9EsbYFJN7o3MTz6YEEuzR2rBTCsiEfim4MMk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0vUD4yU9EsbYFJN7o3MTz6YEEuzR2rBTCsiEfim4MMk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.v0zj4ognzu.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/nwxrrfr11z-v0zj4ognzu.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010892297"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91807"}, {"Name": "ETag", "Value": "\"0vUD4yU9EsbYFJN7o3MTz6YEEuzR2rBTCsiEfim4MMk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0zj4ognzu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.v0zj4ognzu.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "589892"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0zj4ognzu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.v0zj4ognzu.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/nwxrrfr11z-v0zj4ognzu.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0vUD4yU9EsbYFJN7o3MTz6YEEuzR2rBTCsiEfim4MMk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0zj4ognzu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-0vUD4yU9EsbYFJN7o3MTz6YEEuzR2rBTCsiEfim4MMk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.37tfw0ft22.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/7d25u8026p-37tfw0ft22.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030209655"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33101"}, {"Name": "ETag", "Value": "\"H3YVfeyTRT1+*******************************=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "37tfw0ft22"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css"}, {"Name": "integrity", "Value": "sha256-j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.37tfw0ft22.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "280259"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "37tfw0ft22"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css"}, {"Name": "integrity", "Value": "sha256-j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.37tfw0ft22.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/7d25u8026p-37tfw0ft22.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33101"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"H3YVfeyTRT1+*******************************=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "37tfw0ft22"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz"}, {"Name": "integrity", "Value": "sha256-H3YVfeyTRT1+*******************************="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/7d25u8026p-37tfw0ft22.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030209655"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33101"}, {"Name": "ETag", "Value": "\"H3YVfeyTRT1+*******************************=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "280259"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/7d25u8026p-37tfw0ft22.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33101"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"H3YVfeyTRT1+*******************************=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H3YVfeyTRT1+*******************************="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/9d9mvyxx09-hrwsygsryq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008699132"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114953"}, {"Name": "ETag", "Value": "\"JrRzHeTjl4b6Uz7Bw07aa6fJXaq6cvJS2O5NNTMvdsA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrwsygsryq"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "679615"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrwsygsryq"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/9d9mvyxx09-hrwsygsryq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114953"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"JrRzHeTjl4b6Uz7Bw07aa6fJXaq6cvJS2O5NNTMvdsA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrwsygsryq"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz"}, {"Name": "integrity", "Value": "sha256-JrRzHeTjl4b6Uz7Bw07aa6fJXaq6cvJS2O5NNTMvdsA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/9d9mvyxx09-hrwsygsryq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008699132"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114953"}, {"Name": "ETag", "Value": "\"JrRzHeTjl4b6Uz7Bw07aa6fJXaq6cvJS2O5NNTMvdsA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "679615"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/9d9mvyxx09-hrwsygsryq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114953"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"JrRzHeTjl4b6Uz7Bw07aa6fJXaq6cvJS2O5NNTMvdsA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JrRzHeTjl4b6Uz7Bw07aa6fJXaq6cvJS2O5NNTMvdsA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/syw4rzjclx-pk9g2wxc8p.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032271598"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30986"}, {"Name": "ETag", "Value": "\"U4Fb0kGgh99rOTlgKWLxJ9830Vhug5WC4AtpIoTow/A=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232911"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/6qqdvnpsj0-ft3s53vfgj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010904769"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91702"}, {"Name": "ETag", "Value": "\"rGg/dX06muNMQeG+lvJ69DzvgGZZqrpPVSkJcrP/VHE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft3s53vfgj"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "589087"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft3s53vfgj"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/6qqdvnpsj0-ft3s53vfgj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91702"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rGg/dX06muNMQeG+lvJ69DzvgGZZqrpPVSkJcrP/VHE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft3s53vfgj"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-rGg/dX06muNMQeG+lvJ69DzvgGZZqrpPVSkJcrP/VHE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/syw4rzjclx-pk9g2wxc8p.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30986"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"U4Fb0kGgh99rOTlgKWLxJ9830Vhug5WC4AtpIoTow/A=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-U4Fb0kGgh99rOTlgKWLxJ9830Vhug5WC4AtpIoTow/A="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/6qqdvnpsj0-ft3s53vfgj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010904769"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91702"}, {"Name": "ETag", "Value": "\"rGg/dX06muNMQeG+lvJ69DzvgGZZqrpPVSkJcrP/VHE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "589087"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/6qqdvnpsj0-ft3s53vfgj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91702"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rGg/dX06muNMQeG+lvJ69DzvgGZZqrpPVSkJcrP/VHE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rGg/dX06muNMQeG+lvJ69DzvgGZZqrpPVSkJcrP/VHE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.pk9g2wxc8p.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/syw4rzjclx-pk9g2wxc8p.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032271598"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30986"}, {"Name": "ETag", "Value": "\"U4Fb0kGgh99rOTlgKWLxJ9830Vhug5WC4AtpIoTow/A=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pk9g2wxc8p"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.pk9g2wxc8p.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232911"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pk9g2wxc8p"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.pk9g2wxc8p.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/syw4rzjclx-pk9g2wxc8p.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30986"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"U4Fb0kGgh99rOTlgKWLxJ9830Vhug5WC4AtpIoTow/A=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pk9g2wxc8p"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz"}, {"Name": "integrity", "Value": "sha256-U4Fb0kGgh99rOTlgKWLxJ9830Vhug5WC4AtpIoTow/A="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.s35ty4nyc5.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/yppz3hmjub-s35ty4nyc5.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030073379"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33251"}, {"Name": "ETag", "Value": "\"n8W3VdC4I3veW4VnsbrnEGvARFEgXX9cBa7M89gRvWI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s35ty4nyc5"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css"}, {"Name": "integrity", "Value": "sha256-GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.s35ty4nyc5.css", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/css/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "281046"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s35ty4nyc5"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css"}, {"Name": "integrity", "Value": "sha256-GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.s35ty4nyc5.css.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/yppz3hmjub-s35ty4nyc5.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33251"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"n8W3VdC4I3veW4VnsbrnEGvARFEgXX9cBa7M89gRvWI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s35ty4nyc5"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.gz"}, {"Name": "integrity", "Value": "sha256-n8W3VdC4I3veW4VnsbrnEGvARFEgXX9cBa7M89gRvWI="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.6cfz1n2cew.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/otjio8kepc-6cfz1n2cew.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022545373"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44354"}, {"Name": "ETag", "Value": "\"+S1dgJktEranDVAQPXUu9kuAGDv1gLsHGc8IIKVtqYc=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6cfz1n2cew"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js"}, {"Name": "integrity", "Value": "sha256-mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.6cfz1n2cew.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "207819"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6cfz1n2cew"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js"}, {"Name": "integrity", "Value": "sha256-mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.6cfz1n2cew.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/otjio8kepc-6cfz1n2cew.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44354"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+S1dgJktEranDVAQPXUu9kuAGDv1gLsHGc8IIKVtqYc=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6cfz1n2cew"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz"}, {"Name": "integrity", "Value": "sha256-+S1dgJktEranDVAQPXUu9kuAGDv1gLsHGc8IIKVtqYc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/otjio8kepc-6cfz1n2cew.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022545373"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44354"}, {"Name": "ETag", "Value": "\"+S1dgJktEranDVAQPXUu9kuAGDv1gLsHGc8IIKVtqYc=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "207819"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.6pdc2jztkx.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/46sb2pcnts-6pdc2jztkx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010864133"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "92045"}, {"Name": "ETag", "Value": "\"3g4vdMAw6KkaWMu7zxiegTSQXuIuaVWaJntJNk6Do5k=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pdc2jztkx"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, {"Name": "integrity", "Value": "sha256-Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.6pdc2jztkx.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "444579"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pdc2jztkx"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, {"Name": "integrity", "Value": "sha256-Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.6pdc2jztkx.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/46sb2pcnts-6pdc2jztkx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "92045"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3g4vdMAw6KkaWMu7zxiegTSQXuIuaVWaJntJNk6Do5k=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pdc2jztkx"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz"}, {"Name": "integrity", "Value": "sha256-3g4vdMAw6KkaWMu7zxiegTSQXuIuaVWaJntJNk6Do5k="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/otjio8kepc-6cfz1n2cew.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44354"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+S1dgJktEranDVAQPXUu9kuAGDv1gLsHGc8IIKVtqYc=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+S1dgJktEranDVAQPXUu9kuAGDv1gLsHGc8IIKVtqYc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/46sb2pcnts-6pdc2jztkx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010864133"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "92045"}, {"Name": "ETag", "Value": "\"3g4vdMAw6KkaWMu7zxiegTSQXuIuaVWaJntJNk6Do5k=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "444579"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/46sb2pcnts-6pdc2jztkx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "92045"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3g4vdMAw6KkaWMu7zxiegTSQXuIuaVWaJntJNk6Do5k=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3g4vdMAw6KkaWMu7zxiegTSQXuIuaVWaJntJNk6Do5k="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.493y06b0oq.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/vkb8dl48wp-493y06b0oq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041692725"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23984"}, {"Name": "ETag", "Value": "\"DKxtpOFRkjSEaPf7hRuWjmLIaLKqWeH4MMNTVQusPtg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "493y06b0oq"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Name": "integrity", "Value": "sha256-CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.493y06b0oq.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "80721"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "493y06b0oq"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Name": "integrity", "Value": "sha256-CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.493y06b0oq.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/vkb8dl48wp-493y06b0oq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23984"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"DKxtpOFRkjSEaPf7hRuWjmLIaLKqWeH4MMNTVQusPtg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "493y06b0oq"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz"}, {"Name": "integrity", "Value": "sha256-DKxtpOFRkjSEaPf7hRuWjmLIaLKqWeH4MMNTVQusPtg="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/vkb8dl48wp-493y06b0oq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041692725"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23984"}, {"Name": "ETag", "Value": "\"DKxtpOFRkjSEaPf7hRuWjmLIaLKqWeH4MMNTVQusPtg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "80721"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/vkb8dl48wp-493y06b0oq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23984"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"DKxtpOFRkjSEaPf7hRuWjmLIaLKqWeH4MMNTVQusPtg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DKxtpOFRkjSEaPf7hRuWjmLIaLKqWeH4MMNTVQusPtg="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.iovd86k7lj.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/zmeu8j569y-iovd86k7lj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011499937"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86956"}, {"Name": "ETag", "Value": "\"dUvVCTDcbFI5khZmwMe5maCZAf7l+wbCXWpFM2MAcb0=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iovd86k7lj"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, {"Name": "integrity", "Value": "sha256-Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.iovd86k7lj.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "332090"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iovd86k7lj"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, {"Name": "integrity", "Value": "sha256-Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.iovd86k7lj.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/zmeu8j569y-iovd86k7lj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86956"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"dUvVCTDcbFI5khZmwMe5maCZAf7l+wbCXWpFM2MAcb0=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iovd86k7lj"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz"}, {"Name": "integrity", "Value": "sha256-dUvVCTDcbFI5khZmwMe5maCZAf7l+wbCXWpFM2MAcb0="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/zmeu8j569y-iovd86k7lj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011499937"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86956"}, {"Name": "ETag", "Value": "\"dUvVCTDcbFI5khZmwMe5maCZAf7l+wbCXWpFM2MAcb0=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "332090"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/zmeu8j569y-iovd86k7lj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86956"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"dUvVCTDcbFI5khZmwMe5maCZAf7l+wbCXWpFM2MAcb0=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dUvVCTDcbFI5khZmwMe5maCZAf7l+wbCXWpFM2MAcb0="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/x12zmy9w2f-vr1egmr9el.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000034658441"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28852"}, {"Name": "ETag", "Value": "\"5Twc4TJNTMb/v4Us4GQzp7G2D/Azi6nb8XdxLCPc8A0=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "135829"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/x12zmy9w2f-vr1egmr9el.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28852"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5Twc4TJNTMb/v4Us4GQzp7G2D/Azi6nb8XdxLCPc8A0=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5Twc4TJNTMb/v4Us4GQzp7G2D/Azi6nb8XdxLCPc8A0="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.kbrnm935zg.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/cv2idzmv24-kbrnm935zg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015593083"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64130"}, {"Name": "ETag", "Value": "\"0JvrXl3Fun1tyY8CGbKufhGRvhas6A+KnWWHAP5nHDA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kbrnm935zg"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map"}, {"Name": "integrity", "Value": "sha256-EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.kbrnm935zg.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "305438"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kbrnm935zg"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map"}, {"Name": "integrity", "Value": "sha256-EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.kbrnm935zg.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/cv2idzmv24-kbrnm935zg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64130"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0JvrXl3Fun1tyY8CGbKufhGRvhas6A+KnWWHAP5nHDA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kbrnm935zg"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz"}, {"Name": "integrity", "Value": "sha256-0JvrXl3Fun1tyY8CGbKufhGRvhas6A+KnWWHAP5nHDA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/cv2idzmv24-kbrnm935zg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015593083"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64130"}, {"Name": "ETag", "Value": "\"0JvrXl3Fun1tyY8CGbKufhGRvhas6A+KnWWHAP5nHDA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "305438"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/cv2idzmv24-kbrnm935zg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64130"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0JvrXl3Fun1tyY8CGbKufhGRvhas6A+KnWWHAP5nHDA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0JvrXl3Fun1tyY8CGbKufhGRvhas6A+KnWWHAP5nHDA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.jj8uyg4cgr.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/h8lt1guizi-jj8uyg4cgr.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000053659584"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18635"}, {"Name": "ETag", "Value": "\"jT9UAw765SpUas395Rv9uCfj03Yr+71SED9cFddplgA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jj8uyg4cgr"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js"}, {"Name": "integrity", "Value": "sha256-QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.jj8uyg4cgr.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "73935"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jj8uyg4cgr"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js"}, {"Name": "integrity", "Value": "sha256-QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.jj8uyg4cgr.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/h8lt1guizi-jj8uyg4cgr.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18635"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jT9UAw765SpUas395Rv9uCfj03Yr+71SED9cFddplgA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jj8uyg4cgr"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz"}, {"Name": "integrity", "Value": "sha256-jT9UAw765SpUas395Rv9uCfj03Yr+71SED9cFddplgA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/h8lt1guizi-jj8uyg4cgr.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000053659584"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18635"}, {"Name": "ETag", "Value": "\"jT9UAw765SpUas395Rv9uCfj03Yr+71SED9cFddplgA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "73935"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/h8lt1guizi-jj8uyg4cgr.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18635"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jT9UAw765SpUas395Rv9uCfj03Yr+71SED9cFddplgA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jT9UAw765SpUas395Rv9uCfj03Yr+71SED9cFddplgA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/z4zkotwuf3-y7v9cxd14o.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017646644"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56667"}, {"Name": "ETag", "Value": "\"XzeYhXaQJrHdbijtyDQZA554STHRsDLunRk6rrp9uiA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "222455"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/z4zkotwuf3-y7v9cxd14o.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56667"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"XzeYhXaQJrHdbijtyDQZA554STHRsDLunRk6rrp9uiA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XzeYhXaQJrHdbijtyDQZA554STHRsDLunRk6rrp9uiA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.y7v9cxd14o.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/z4zkotwuf3-y7v9cxd14o.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017646644"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56667"}, {"Name": "ETag", "Value": "\"XzeYhXaQJrHdbijtyDQZA554STHRsDLunRk6rrp9uiA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y7v9cxd14o"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}, {"Name": "integrity", "Value": "sha256-Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.y7v9cxd14o.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "222455"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y7v9cxd14o"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}, {"Name": "integrity", "Value": "sha256-Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.y7v9cxd14o.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/z4zkotwuf3-y7v9cxd14o.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56667"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"XzeYhXaQJrHdbijtyDQZA554STHRsDLunRk6rrp9uiA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y7v9cxd14o"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz"}, {"Name": "integrity", "Value": "sha256-XzeYhXaQJrHdbijtyDQZA554STHRsDLunRk6rrp9uiA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.vr1egmr9el.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/x12zmy9w2f-vr1egmr9el.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000034658441"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28852"}, {"Name": "ETag", "Value": "\"5Twc4TJNTMb/v4Us4GQzp7G2D/Azi6nb8XdxLCPc8A0=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vr1egmr9el"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js"}, {"Name": "integrity", "Value": "sha256-exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.vr1egmr9el.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "135829"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vr1egmr9el"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js"}, {"Name": "integrity", "Value": "sha256-exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.vr1egmr9el.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/x12zmy9w2f-vr1egmr9el.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28852"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5Twc4TJNTMb/v4Us4GQzp7G2D/Azi6nb8XdxLCPc8A0=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vr1egmr9el"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.gz"}, {"Name": "integrity", "Value": "sha256-5Twc4TJNTMb/v4Us4GQzp7G2D/Azi6nb8XdxLCPc8A0="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/qq7vsmffi6-notf2xhcfb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000033818059"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "29569"}, {"Name": "ETag", "Value": "\"kVm75Syt0B86ikuL4igEkX315p71WyMLSh+YvGyq2ZY=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "145401"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/qq7vsmffi6-notf2xhcfb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "29569"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kVm75Syt0B86ikuL4igEkX315p71WyMLSh+YvGyq2ZY=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kVm75Syt0B86ikuL4igEkX315p71WyMLSh+YvGyq2ZY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.h1s4sie4z3.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/gye3uq2aze-h1s4sie4z3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015522166"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64423"}, {"Name": "ETag", "Value": "\"u2mOk/RK572xHpx+p8mZVoEsMjVVs6FWDy+64B1PiEg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h1s4sie4z3"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map"}, {"Name": "integrity", "Value": "sha256-9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.h1s4sie4z3.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "306606"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h1s4sie4z3"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map"}, {"Name": "integrity", "Value": "sha256-9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.h1s4sie4z3.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/gye3uq2aze-h1s4sie4z3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64423"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"u2mOk/RK572xHpx+p8mZVoEsMjVVs6FWDy+64B1PiEg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h1s4sie4z3"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map.gz"}, {"Name": "integrity", "Value": "sha256-u2mOk/RK572xHpx+p8mZVoEsMjVVs6FWDy+64B1PiEg="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/gye3uq2aze-h1s4sie4z3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015522166"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64423"}, {"Name": "ETag", "Value": "\"u2mOk/RK572xHpx+p8mZVoEsMjVVs6FWDy+64B1PiEg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "306606"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/gye3uq2aze-h1s4sie4z3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64423"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"u2mOk/RK572xHpx+p8mZVoEsMjVVs6FWDy+64B1PiEg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u2mOk/RK572xHpx+p8mZVoEsMjVVs6FWDy+64B1PiEg="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.63fj8s7r0e.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/4f5ighps57-63fj8s7r0e.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000060106990"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16636"}, {"Name": "ETag", "Value": "\"axjpMrpO28RCO+lbVo53yegI7s+b3vU0DGRS48HcQ8U=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "63fj8s7r0e"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js"}, {"Name": "integrity", "Value": "sha256-3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.63fj8s7r0e.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "60635"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "63fj8s7r0e"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js"}, {"Name": "integrity", "Value": "sha256-3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.63fj8s7r0e.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/4f5ighps57-63fj8s7r0e.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16636"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"axjpMrpO28RCO+lbVo53yegI7s+b3vU0DGRS48HcQ8U=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "63fj8s7r0e"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.gz"}, {"Name": "integrity", "Value": "sha256-axjpMrpO28RCO+lbVo53yegI7s+b3vU0DGRS48HcQ8U="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/4f5ighps57-63fj8s7r0e.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000060106990"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16636"}, {"Name": "ETag", "Value": "\"axjpMrpO28RCO+lbVo53yegI7s+b3vU0DGRS48HcQ8U=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "60635"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.0j3bgjxly4.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/p1y9zxb3a5-0j3bgjxly4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017905424"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55848"}, {"Name": "ETag", "Value": "\"+Eq85CUhXtCIudgWXb2bTNfSCy1lIH6+A8wRel8gj8o=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0j3bgjxly4"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map"}, {"Name": "integrity", "Value": "sha256-ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.0j3bgjxly4.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "220561"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0j3bgjxly4"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map"}, {"Name": "integrity", "Value": "sha256-ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.0j3bgjxly4.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/p1y9zxb3a5-0j3bgjxly4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55848"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"+Eq85CUhXtCIudgWXb2bTNfSCy1lIH6+A8wRel8gj8o=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0j3bgjxly4"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz"}, {"Name": "integrity", "Value": "sha256-+Eq85CUhXtCIudgWXb2bTNfSCy1lIH6+A8wRel8gj8o="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/4f5ighps57-63fj8s7r0e.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16636"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"axjpMrpO28RCO+lbVo53yegI7s+b3vU0DGRS48HcQ8U=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-axjpMrpO28RCO+lbVo53yegI7s+b3vU0DGRS48HcQ8U="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/p1y9zxb3a5-0j3bgjxly4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017905424"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55848"}, {"Name": "ETag", "Value": "\"+Eq85CUhXtCIudgWXb2bTNfSCy1lIH6+A8wRel8gj8o=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "220561"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/p1y9zxb3a5-0j3bgjxly4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55848"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"+Eq85CUhXtCIudgWXb2bTNfSCy1lIH6+A8wRel8gj8o=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+Eq85CUhXtCIudgWXb2bTNfSCy1lIH6+A8wRel8gj8o="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.notf2xhcfb.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/qq7vsmffi6-notf2xhcfb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000033818059"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "29569"}, {"Name": "ETag", "Value": "\"kVm75Syt0B86ikuL4igEkX315p71WyMLSh+YvGyq2ZY=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "notf2xhcfb"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js"}, {"Name": "integrity", "Value": "sha256-+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.notf2xhcfb.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/dist/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "145401"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "notf2xhcfb"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js"}, {"Name": "integrity", "Value": "sha256-+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.notf2xhcfb.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/qq7vsmffi6-notf2xhcfb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "29569"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kVm75Syt0B86ikuL4igEkX315p71WyMLSh+YvGyq2ZY=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "notf2xhcfb"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.gz"}, {"Name": "integrity", "Value": "sha256-kVm75Syt0B86ikuL4igEkX315p71WyMLSh+YvGyq2ZY="}]}, {"Route": "lib/bootstrap/LICENSE", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk="}]}, {"Route": "lib/bootstrap/LICENSE.81b7ukuj9c", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/bootstrap/LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "81b7ukuj9c"}, {"Name": "label", "Value": "lib/bootstrap/LICENSE"}, {"Name": "integrity", "Value": "sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.47otxtyo56.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/0hn9qlz7yz-47otxtyo56.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000214961307"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4651"}, {"Name": "ETag", "Value": "\"4PypidGgV/pb6NTdbW3MxWNol0BeoGyycmZym1ulQac=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "47otxtyo56"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js"}, {"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.47otxtyo56.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19385"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "47otxtyo56"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js"}, {"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.47otxtyo56.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/0hn9qlz7yz-47otxtyo56.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4PypidGgV/pb6NTdbW3MxWNol0BeoGyycmZym1ulQac=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "47otxtyo56"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz"}, {"Name": "integrity", "Value": "sha256-4PypidGgV/pb6NTdbW3MxWNol0BeoGyycmZym1ulQac="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/0hn9qlz7yz-47otxtyo56.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000214961307"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4651"}, {"Name": "ETag", "Value": "\"4PypidGgV/pb6NTdbW3MxWNol0BeoGyycmZym1ulQac=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19385"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/0hn9qlz7yz-47otxtyo56.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4PypidGgV/pb6NTdbW3MxWNol0BeoGyycmZym1ulQac=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4PypidGgV/pb6NTdbW3MxWNol0BeoGyycmZym1ulQac="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.4v8eqarkd7.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/3swqra1zoe-4v8eqarkd7.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000452898551"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2207"}, {"Name": "ETag", "Value": "\"X5v6p2DF+RAiFhn86edCn2BMm85rL/1BfawJ7eYKiaY=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4v8eqarkd7"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js"}, {"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.4v8eqarkd7.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5824"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4v8eqarkd7"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js"}, {"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.4v8eqarkd7.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/3swqra1zoe-4v8eqarkd7.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2207"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"X5v6p2DF+RAiFhn86edCn2BMm85rL/1BfawJ7eYKiaY=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4v8eqarkd7"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz"}, {"Name": "integrity", "Value": "sha256-X5v6p2DF+RAiFhn86edCn2BMm85rL/1BfawJ7eYKiaY="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/3swqra1zoe-4v8eqarkd7.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000452898551"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2207"}, {"Name": "ETag", "Value": "\"X5v6p2DF+RAiFhn86edCn2BMm85rL/1BfawJ7eYKiaY=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5824"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/3swqra1zoe-4v8eqarkd7.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2207"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"X5v6p2DF+RAiFhn86edCn2BMm85rL/1BfawJ7eYKiaY=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-X5v6p2DF+RAiFhn86edCn2BMm85rL/1BfawJ7eYKiaY="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.356vix0kms.txt", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/zfueim4itm-356vix0kms.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001438848921"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "694"}, {"Name": "ETag", "Value": "\"APtdKLCc/nK1prhFh6ytj7bT4olz3PpIJvWoVVDWRGQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "356vix0kms"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/LICENSE.txt"}, {"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.356vix0kms.txt", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1139"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "356vix0kms"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/LICENSE.txt"}, {"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.356vix0kms.txt.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/zfueim4itm-356vix0kms.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "694"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"APtdKLCc/nK1prhFh6ytj7bT4olz3PpIJvWoVVDWRGQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "356vix0kms"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz"}, {"Name": "integrity", "Value": "sha256-APtdKLCc/nK1prhFh6ytj7bT4olz3PpIJvWoVVDWRGQ="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/zfueim4itm-356vix0kms.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001438848921"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "694"}, {"Name": "ETag", "Value": "\"APtdKLCc/nK1prhFh6ytj7bT4olz3PpIJvWoVVDWRGQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1139"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/zfueim4itm-356vix0kms.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "694"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"APtdKLCc/nK1prhFh6ytj7bT4olz3PpIJvWoVVDWRGQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-APtdKLCc/nK1prhFh6ytj7bT4olz3PpIJvWoVVDWRGQ="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.83jwlth58m.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/x4fl886j3g-83jwlth58m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071027772"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14078"}, {"Name": "ETag", "Value": "\"OKcAH26EdtYnFJnGz+MbWB/hAFCDtD3z03cV7nsNxlg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "83jwlth58m"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.js"}, {"Name": "integrity", "Value": "sha256-XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.83jwlth58m.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/dist/additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "53033"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "83jwlth58m"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.js"}, {"Name": "integrity", "Value": "sha256-XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.83jwlth58m.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/x4fl886j3g-83jwlth58m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14078"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OKcAH26EdtYnFJnGz+MbWB/hAFCDtD3z03cV7nsNxlg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "83jwlth58m"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.js.gz"}, {"Name": "integrity", "Value": "sha256-OKcAH26EdtYnFJnGz+MbWB/hAFCDtD3z03cV7nsNxlg="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/x4fl886j3g-83jwlth58m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071027772"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14078"}, {"Name": "ETag", "Value": "\"OKcAH26EdtYnFJnGz+MbWB/hAFCDtD3z03cV7nsNxlg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/dist/additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "53033"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/x4fl886j3g-83jwlth58m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14078"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OKcAH26EdtYnFJnGz+MbWB/hAFCDtD3z03cV7nsNxlg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OKcAH26EdtYnFJnGz+MbWB/hAFCDtD3z03cV7nsNxlg="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/kh3yuzknfi-mrlpezrjn3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000154249576"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6482"}, {"Name": "ETag", "Value": "\"7FTIZ9Sy+RoTM5k0vROIfrcx1T2djqzEhoWUTyEGYdk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22125"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/kh3yuzknfi-mrlpezrjn3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6482"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"7FTIZ9Sy+RoTM5k0vROIfrcx1T2djqzEhoWUTyEGYdk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7FTIZ9Sy+RoTM5k0vROIfrcx1T2djqzEhoWUTyEGYdk="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.mrlpezrjn3.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/kh3yuzknfi-mrlpezrjn3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000154249576"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6482"}, {"Name": "ETag", "Value": "\"7FTIZ9Sy+RoTM5k0vROIfrcx1T2djqzEhoWUTyEGYdk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mrlpezrjn3"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.min.js"}, {"Name": "integrity", "Value": "sha256-jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.mrlpezrjn3.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22125"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mrlpezrjn3"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.min.js"}, {"Name": "integrity", "Value": "sha256-jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.mrlpezrjn3.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/kh3yuzknfi-mrlpezrjn3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6482"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"7FTIZ9Sy+RoTM5k0vROIfrcx1T2djqzEhoWUTyEGYdk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mrlpezrjn3"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.min.js.gz"}, {"Name": "integrity", "Value": "sha256-7FTIZ9Sy+RoTM5k0vROIfrcx1T2djqzEhoWUTyEGYdk="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/okhcuobryv-lzl9nlhx6b.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071078257"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14068"}, {"Name": "ETag", "Value": "\"qfPxa9GDxZoylcELsgG9gebdHGdnkzq24NR+WZjDU4w=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/dist/jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "52536"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/okhcuobryv-lzl9nlhx6b.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14068"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qfPxa9GDxZoylcELsgG9gebdHGdnkzq24NR+WZjDU4w=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qfPxa9GDxZoylcELsgG9gebdHGdnkzq24NR+WZjDU4w="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.lzl9nlhx6b.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/okhcuobryv-lzl9nlhx6b.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071078257"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14068"}, {"Name": "ETag", "Value": "\"qfPxa9GDxZoylcELsgG9gebdHGdnkzq24NR+WZjDU4w=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lzl9nlhx6b"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.js"}, {"Name": "integrity", "Value": "sha256-kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.lzl9nlhx6b.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/dist/jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "52536"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lzl9nlhx6b"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.js"}, {"Name": "integrity", "Value": "sha256-kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.lzl9nlhx6b.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/okhcuobryv-lzl9nlhx6b.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14068"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qfPxa9GDxZoylcELsgG9gebdHGdnkzq24NR+WZjDU4w=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lzl9nlhx6b"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.js.gz"}, {"Name": "integrity", "Value": "sha256-qfPxa9GDxZoylcELsgG9gebdHGdnkzq24NR+WZjDU4w="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.ag7o75518u.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/gpo7e5xb47-ag7o75518u.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000123122384"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8121"}, {"Name": "ETag", "Value": "\"YgOEl7ge6Ki6+76DGHj7yoX9Qs+JWj09WvIFSjhk2cs=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ag7o75518u"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.min.js"}, {"Name": "integrity", "Value": "sha256-umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.ag7o75518u.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25308"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ag7o75518u"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.min.js"}, {"Name": "integrity", "Value": "sha256-umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.ag7o75518u.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/gpo7e5xb47-ag7o75518u.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8121"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YgOEl7ge6Ki6+76DGHj7yoX9Qs+JWj09WvIFSjhk2cs=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ag7o75518u"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.min.js.gz"}, {"Name": "integrity", "Value": "sha256-YgOEl7ge6Ki6+76DGHj7yoX9Qs+JWj09WvIFSjhk2cs="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/gpo7e5xb47-ag7o75518u.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000123122384"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8121"}, {"Name": "ETag", "Value": "\"YgOEl7ge6Ki6+76DGHj7yoX9Qs+JWj09WvIFSjhk2cs=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25308"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/gpo7e5xb47-ag7o75518u.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8121"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YgOEl7ge6Ki6+76DGHj7yoX9Qs+JWj09WvIFSjhk2cs=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YgOEl7ge6Ki6+76DGHj7yoX9Qs+JWj09WvIFSjhk2cs="}]}, {"Route": "lib/jquery-validation/LICENSE.md", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/3dipqyym91-x0q3zqp4vz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001461988304"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "683"}, {"Name": "ETag", "Value": "\"df0X2bOJ06A+a9mf19qzQWvt7CCNnAaCX0yWSHwxdUE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "W/\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation/LICENSE.md", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation/LICENSE.md.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/3dipqyym91-x0q3zqp4vz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "683"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"df0X2bOJ06A+a9mf19qzQWvt7CCNnAaCX0yWSHwxdUE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-df0X2bOJ06A+a9mf19qzQWvt7CCNnAaCX0yWSHwxdUE="}]}, {"Route": "lib/jquery-validation/LICENSE.x0q3zqp4vz.md", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/3dipqyym91-x0q3zqp4vz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001461988304"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "683"}, {"Name": "ETag", "Value": "\"df0X2bOJ06A+a9mf19qzQWvt7CCNnAaCX0yWSHwxdUE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "W/\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0q3zqp4vz"}, {"Name": "label", "Value": "lib/jquery-validation/LICENSE.md"}, {"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation/LICENSE.x0q3zqp4vz.md", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery-validation/LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0q3zqp4vz"}, {"Name": "label", "Value": "lib/jquery-validation/LICENSE.md"}, {"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation/LICENSE.x0q3zqp4vz.md.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/3dipqyym91-x0q3zqp4vz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "683"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"df0X2bOJ06A+a9mf19qzQWvt7CCNnAaCX0yWSHwxdUE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0q3zqp4vz"}, {"Name": "label", "Value": "lib/jquery-validation/LICENSE.md.gz"}, {"Name": "integrity", "Value": "sha256-df0X2bOJ06A+a9mf19qzQWvt7CCNnAaCX0yWSHwxdUE="}]}, {"Route": "lib/jquery/dist/jquery.0i3buxo5is.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/cxlmau4v5s-0i3buxo5is.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011843851"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "84431"}, {"Name": "ETag", "Value": "\"u1a19QoSpVBP/3/GNlsnGb5GykPh2UehRIZmQppbtaw=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0i3buxo5is"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.js"}, {"Name": "integrity", "Value": "sha256-e<PERSON>hayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4="}]}, {"Route": "lib/jquery/dist/jquery.0i3buxo5is.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "285314"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0i3buxo5is"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.js"}, {"Name": "integrity", "Value": "sha256-e<PERSON>hayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4="}]}, {"Route": "lib/jquery/dist/jquery.0i3buxo5is.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/cxlmau4v5s-0i3buxo5is.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "84431"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"u1a19QoSpVBP/3/GNlsnGb5GykPh2UehRIZmQppbtaw=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0i3buxo5is"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.js.gz"}, {"Name": "integrity", "Value": "sha256-u1a19QoSpVBP/3/GNlsnGb5GykPh2UehRIZmQppbtaw="}]}, {"Route": "lib/jquery/dist/jquery.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/cxlmau4v5s-0i3buxo5is.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011843851"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "84431"}, {"Name": "ETag", "Value": "\"u1a19QoSpVBP/3/GNlsnGb5GykPh2UehRIZmQppbtaw=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-e<PERSON>hayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4="}]}, {"Route": "lib/jquery/dist/jquery.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "285314"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-e<PERSON>hayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4="}]}, {"Route": "lib/jquery/dist/jquery.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/cxlmau4v5s-0i3buxo5is.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "84431"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"u1a19QoSpVBP/3/GNlsnGb5GykPh2UehRIZmQppbtaw=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u1a19QoSpVBP/3/GNlsnGb5GykPh2UehRIZmQppbtaw="}]}, {"Route": "lib/jquery/dist/jquery.min.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/8nqm6i12zt-o1o13a6vjx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032590275"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30683"}, {"Name": "ETag", "Value": "\"lQJ7hfi78sXWfeYzRPHYaorepUtSAgUubKKf7dCETlA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo="}]}, {"Route": "lib/jquery/dist/jquery.min.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "87533"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo="}]}, {"Route": "lib/jquery/dist/jquery.min.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/8nqm6i12zt-o1o13a6vjx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30683"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lQJ7hfi78sXWfeYzRPHYaorepUtSAgUubKKf7dCETlA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lQJ7hfi78sXWfeYzRPHYaorepUtSAgUubKKf7dCETlA="}]}, {"Route": "lib/jquery/dist/jquery.min.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/pw8pr2kp2i-ttgo8qnofa.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000018363112"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54456"}, {"Name": "ETag", "Value": "\"8epPvrp0xBXMxP0dnv0Axc8mOq0ExnxVGCD6PJLwD7k=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg="}]}, {"Route": "lib/jquery/dist/jquery.min.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "134755"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg="}]}, {"Route": "lib/jquery/dist/jquery.min.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/pw8pr2kp2i-ttgo8qnofa.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54456"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8epPvrp0xBXMxP0dnv0Axc8mOq0ExnxVGCD6PJLwD7k=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8epPvrp0xBXMxP0dnv0Axc8mOq0ExnxVGCD6PJLwD7k="}]}, {"Route": "lib/jquery/dist/jquery.min.o1o13a6vjx.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/8nqm6i12zt-o1o13a6vjx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032590275"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30683"}, {"Name": "ETag", "Value": "\"lQJ7hfi78sXWfeYzRPHYaorepUtSAgUubKKf7dCETlA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o1o13a6vjx"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.js"}, {"Name": "integrity", "Value": "sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo="}]}, {"Route": "lib/jquery/dist/jquery.min.o1o13a6vjx.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "87533"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o1o13a6vjx"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.js"}, {"Name": "integrity", "Value": "sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo="}]}, {"Route": "lib/jquery/dist/jquery.min.o1o13a6vjx.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/8nqm6i12zt-o1o13a6vjx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30683"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lQJ7hfi78sXWfeYzRPHYaorepUtSAgUubKKf7dCETlA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o1o13a6vjx"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.js.gz"}, {"Name": "integrity", "Value": "sha256-lQJ7hfi78sXWfeYzRPHYaorepUtSAgUubKKf7dCETlA="}]}, {"Route": "lib/jquery/dist/jquery.min.ttgo8qnofa.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/pw8pr2kp2i-ttgo8qnofa.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000018363112"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54456"}, {"Name": "ETag", "Value": "\"8epPvrp0xBXMxP0dnv0Axc8mOq0ExnxVGCD6PJLwD7k=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ttgo8qnofa"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.map"}, {"Name": "integrity", "Value": "sha256-z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg="}]}, {"Route": "lib/jquery/dist/jquery.min.ttgo8qnofa.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "134755"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ttgo8qnofa"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.map"}, {"Name": "integrity", "Value": "sha256-z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg="}]}, {"Route": "lib/jquery/dist/jquery.min.ttgo8qnofa.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/pw8pr2kp2i-ttgo8qnofa.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54456"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8epPvrp0xBXMxP0dnv0Axc8mOq0ExnxVGCD6PJLwD7k=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ttgo8qnofa"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.map.gz"}, {"Name": "integrity", "Value": "sha256-8epPvrp0xBXMxP0dnv0Axc8mOq0ExnxVGCD6PJLwD7k="}]}, {"Route": "lib/jquery/dist/jquery.slim.2z0ns9nrw6.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/q9s2mgx7op-2z0ns9nrw6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000014576834"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "68601"}, {"Name": "ETag", "Value": "\"j/v/J/tax404y6srAmEl0GjGbqMVN3aVVGkR3t0Jexw=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2z0ns9nrw6"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.js"}, {"Name": "integrity", "Value": "sha256-UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc="}]}, {"Route": "lib/jquery/dist/jquery.slim.2z0ns9nrw6.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.slim.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232015"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2z0ns9nrw6"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.js"}, {"Name": "integrity", "Value": "sha256-UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc="}]}, {"Route": "lib/jquery/dist/jquery.slim.2z0ns9nrw6.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/q9s2mgx7op-2z0ns9nrw6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "68601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"j/v/J/tax404y6srAmEl0GjGbqMVN3aVVGkR3t0Jexw=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2z0ns9nrw6"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.js.gz"}, {"Name": "integrity", "Value": "sha256-j/v/J/tax404y6srAmEl0GjGbqMVN3aVVGkR3t0Jexw="}]}, {"Route": "lib/jquery/dist/jquery.slim.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/q9s2mgx7op-2z0ns9nrw6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000014576834"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "68601"}, {"Name": "ETag", "Value": "\"j/v/J/tax404y6srAmEl0GjGbqMVN3aVVGkR3t0Jexw=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc="}]}, {"Route": "lib/jquery/dist/jquery.slim.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.slim.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232015"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc="}]}, {"Route": "lib/jquery/dist/jquery.slim.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/q9s2mgx7op-2z0ns9nrw6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "68601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"j/v/J/tax404y6srAmEl0GjGbqMVN3aVVGkR3t0Jexw=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j/v/J/tax404y6srAmEl0GjGbqMVN3aVVGkR3t0Jexw="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.87fc7y1x7t.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/bg0bosjvju-87fc7y1x7t.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000023188944"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "43123"}, {"Name": "ETag", "Value": "\"7hpoe4sIU0dU1PutwtmtqeLFZasIJnQeD5iGeXrySb4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "87fc7y1x7t"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.min.map"}, {"Name": "integrity", "Value": "sha256-9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.87fc7y1x7t.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.slim.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107143"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "87fc7y1x7t"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.min.map"}, {"Name": "integrity", "Value": "sha256-9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.87fc7y1x7t.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/bg0bosjvju-87fc7y1x7t.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "43123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7hpoe4sIU0dU1PutwtmtqeLFZasIJnQeD5iGeXrySb4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "87fc7y1x7t"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.min.map.gz"}, {"Name": "integrity", "Value": "sha256-7hpoe4sIU0dU1PutwtmtqeLFZasIJnQeD5iGeXrySb4="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/w1qcg7ny2i-muycvpuwrr.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041049218"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24360"}, {"Name": "ETag", "Value": "\"WBpiN7hQEKwBhPHtE1Y1X30ghH9XVaxvnIrfAk0S0nM=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.slim.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70264"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/w1qcg7ny2i-muycvpuwrr.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24360"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WBpiN7hQEKwBhPHtE1Y1X30ghH9XVaxvnIrfAk0S0nM=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WBpiN7hQEKwBhPHtE1Y1X30ghH9XVaxvnIrfAk0S0nM="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/bg0bosjvju-87fc7y1x7t.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000023188944"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "43123"}, {"Name": "ETag", "Value": "\"7hpoe4sIU0dU1PutwtmtqeLFZasIJnQeD5iGeXrySb4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.map", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.slim.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107143"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/bg0bosjvju-87fc7y1x7t.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "43123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7hpoe4sIU0dU1PutwtmtqeLFZasIJnQeD5iGeXrySb4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7hpoe4sIU0dU1PutwtmtqeLFZasIJnQeD5iGeXrySb4="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.muycvpuwrr.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/w1qcg7ny2i-muycvpuwrr.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041049218"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24360"}, {"Name": "ETag", "Value": "\"WBpiN7hQEKwBhPHtE1Y1X30ghH9XVaxvnIrfAk0S0nM=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "muycvpuwrr"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.min.js"}, {"Name": "integrity", "Value": "sha256-kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.muycvpuwrr.js", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/dist/jquery.slim.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70264"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "muycvpuwrr"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.min.js"}, {"Name": "integrity", "Value": "sha256-kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.muycvpuwrr.js.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/w1qcg7ny2i-muycvpuwrr.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24360"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WBpiN7hQEKwBhPHtE1Y1X30ghH9XVaxvnIrfAk0S0nM=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "muycvpuwrr"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.min.js.gz"}, {"Name": "integrity", "Value": "sha256-WBpiN7hQEKwBhPHtE1Y1X30ghH9XVaxvnIrfAk0S0nM="}]}, {"Route": "lib/jquery/LICENSE.mlv21k5csn.txt", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/uwnbtgywmj-mlv21k5csn.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001464128843"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "682"}, {"Name": "ETag", "Value": "\"F1wMlgb/juW08DFUssW6Pm9EBKfjXXRiSK0F5FgXxJU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mlv21k5csn"}, {"Name": "label", "Value": "lib/jquery/LICENSE.txt"}, {"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}]}, {"Route": "lib/jquery/LICENSE.mlv21k5csn.txt", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mlv21k5csn"}, {"Name": "label", "Value": "lib/jquery/LICENSE.txt"}, {"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}]}, {"Route": "lib/jquery/LICENSE.mlv21k5csn.txt.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/uwnbtgywmj-mlv21k5csn.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "682"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"F1wMlgb/juW08DFUssW6Pm9EBKfjXXRiSK0F5FgXxJU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mlv21k5csn"}, {"Name": "label", "Value": "lib/jquery/LICENSE.txt.gz"}, {"Name": "integrity", "Value": "sha256-F1wMlgb/juW08DFUssW6Pm9EBKfjXXRiSK0F5FgXxJU="}]}, {"Route": "lib/jquery/LICENSE.txt", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/uwnbtgywmj-mlv21k5csn.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001464128843"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "682"}, {"Name": "ETag", "Value": "\"F1wMlgb/juW08DFUssW6Pm9EBKfjXXRiSK0F5FgXxJU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}]}, {"Route": "lib/jquery/LICENSE.txt", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/lib/jquery/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 06:38:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}]}, {"Route": "lib/jquery/LICENSE.txt.gz", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/Debug/net9.0/compressed/uwnbtgywmj-mlv21k5csn.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "682"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"F1wMlgb/juW08DFUssW6Pm9EBKfjXXRiSK0F5FgXxJU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-F1wMlgb/juW08DFUssW6Pm9EBKfjXXRiSK0F5FgXxJU="}]}, {"Route": "uploads/products/3dd3392a-ee7a-4ea0-93b6-271ce8cfbba9_prod-4.png", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/uploads/products/3dd3392a-ee7a-4ea0-93b6-271ce8cfbba9_prod-4.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "226458"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"MJUREZfhPS3ojZlF6uuGRDqZyixXr/8RDQ/tHL1uDjI=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 08:26:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MJUREZfhPS3ojZlF6uuGRDqZyixXr/8RDQ/tHL1uDjI="}]}, {"Route": "uploads/products/3dd3392a-ee7a-4ea0-93b6-271ce8cfbba9_prod-4.sxlfuk6pbp.png", "AssetFile": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/wwwroot/uploads/products/3dd3392a-ee7a-4ea0-93b6-271ce8cfbba9_prod-4.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "226458"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"MJUREZfhPS3ojZlF6uuGRDqZyixXr/8RDQ/tHL1uDjI=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 08:26:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sxlfuk6pbp"}, {"Name": "label", "Value": "uploads/products/3dd3392a-ee7a-4ea0-93b6-271ce8cfbba9_prod-4.png"}, {"Name": "integrity", "Value": "sha256-MJUREZfhPS3ojZlF6uuGRDqZyixXr/8RDQ/tHL1uDjI="}]}]}