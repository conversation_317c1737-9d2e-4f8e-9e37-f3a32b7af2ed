{"format": 1, "restore": {"/Users/<USER>/Desktop/Sang Nguyen/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/HSU-NguyenHoangThanhSang_22207613.csproj": {}}, "projects": {"/Users/<USER>/Desktop/Sang Nguyen/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/HSU-NguyenHoangThanhSang_22207613.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/HSU-NguyenHoangThanhSang_22207613.csproj", "projectName": "HSU-<PERSON><PERSON><PERSON><PERSON><PERSON>gThanhSang_22207613", "projectPath": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/HSU-NguyenHoangThanhSang_22207613.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Desktop/<PERSON>/Sources/HSU/dotNet/HSU-NguyenHoangThanhSang_22207613/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.303/PortableRuntimeIdentifierGraph.json"}}}}}