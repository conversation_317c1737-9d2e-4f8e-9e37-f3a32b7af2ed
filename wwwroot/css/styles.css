* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    margin: 0;
    padding: 0;
}

/* === TYPOGRAPHY === */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

a {
    color: var(--text-link);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--text-link-hover);
    text-decoration: underline;
}

/* === LAYOUT === */
.main-wrapper {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: var(--sidebar-width);
    background: var(--sidebar-bg);
    color: var(--sidebar-text);
    z-index: var(--z-fixed);
    transition: all var(--transition-normal);
    overflow-y: auto;
}

.sidebar-header {
    padding: var(--spacing-lg);
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-menu {
    padding: var(--spacing-md) 0;
}

.sidebar-menu .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    transition: all var(--transition-fast);
    display: block;
    text-decoration: none;
}

.sidebar-menu .nav-link:hover,
.sidebar-menu .nav-link.active {
    color: var(--white);
    background-color: var(--sidebar-hover);
    border-left: 3px solid var(--white);
}

.main-content {
    margin-left: var(--sidebar-width);
    min-height: 100vh;
}

.top-navbar {
    background: var(--white);
    box-shadow: var(--card-shadow);
    padding: var(--spacing-md) var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.content-wrapper {
    padding: 0 var(--spacing-xl) var(--spacing-xl) var(--spacing-xl);
}

/* === NAVBAR === */
.navbar-brand {
    white-space: normal;
    text-align: center;
    word-break: break-all;
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-xl);
}

.navbar {
    background: var(--sidebar-bg) !important;
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--card-shadow);
}

.navbar-nav .nav-link {
    color: var(--sidebar-text) !important;
    font-weight: var(--font-weight-medium);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    transition: background-color var(--transition-fast);
}

.navbar-nav .nav-link:hover {
    background-color: var(--sidebar-hover);
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    font-weight: var(--font-weight-semibold);
}

/* === BUTTONS === */
.btn {
    font-weight: var(--font-weight-medium);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    transition: all var(--transition-fast);
    box-shadow: var(--btn-shadow);
    border: none;
}

.btn:hover {
    box-shadow: var(--btn-shadow-hover);
    transform: translateY(-1px);
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    color: var(--white);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--white);
}

.btn-secondary:hover {
    background-color: var(--secondary-hover);
    color: var(--white);
}

.btn-success {
    background-color: var(--success-color);
    color: var(--white);
}

.btn-success:hover {
    background-color: var(--success-hover);
    color: var(--white);
}

.btn-warning {
    background-color: var(--warning-color);
    color: var(--dark);
}

.btn-warning:hover {
    background-color: var(--warning-hover);
    color: var(--dark);
}

.btn-danger {
    background-color: var(--danger-color);
    color: var(--white);
}

.btn-danger:hover {
    background-color: var(--danger-hover);
    color: var(--white);
}

.btn-info {
    background-color: var(--info-color);
    color: var(--white);
}

.btn-info:hover {
    background-color: var(--info-hover);
    color: var(--white);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background-color: transparent;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

/* === CARDS === */
.card {
    background-color: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--card-shadow);
    transition: box-shadow var(--transition-normal);
    margin-bottom: var(--spacing-lg);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem var(--shadow-medium);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-light), var(--secondary-light));
    border-bottom: 1px solid var(--card-border);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.card-title {
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
}

.card-body {
    padding: var(--spacing-lg);
}

/* === TABLES === */
.table {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--dark), var(--dark-gray));
    color: var(--white);
    font-weight: var(--font-weight-semibold);
    border: none;
    padding: var(--spacing-md);
}

.table tbody td {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--table-border);
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--table-stripe-bg);
}

.table-hover tbody tr:hover {
    background-color: var(--table-hover-bg);
}

/* === FORMS === */
.form-control {
    border: 2px solid var(--input-border);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-control:focus {
    border-color: var(--input-focus-border);
    box-shadow: var(--input-focus-shadow);
    outline: none;
}

.form-label {
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

/* === ALERTS === */
.alert {
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border: none;
    font-weight: var(--font-weight-medium);
}

.alert-success {
    background-color: var(--success-light);
    color: var(--success-dark);
}

.alert-warning {
    background-color: var(--warning-light);
    color: var(--warning-dark);
}

.alert-danger {
    background-color: var(--danger-light);
    color: var(--danger-dark);
}

.alert-info {
    background-color: var(--info-light);
    color: var(--info-dark);
}

/* === BADGES === */
.badge {
    font-weight: var(--font-weight-medium);
    border-radius: var(--border-radius-full);
    padding: var(--spacing-xs) var(--spacing-sm);
}

/* === BORDERS === */
.border-top {
    border-top: 1px solid var(--border-light) !important;
}

.border-bottom {
    border-bottom: 1px solid var(--border-light) !important;
}

/* === SHADOWS === */
.box-shadow {
    box-shadow: 0 0.25rem 0.75rem var(--shadow-light);
}

/* === FOOTER === */
.footer {
    background-color: var(--bg-tertiary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-md) 0;
    margin-top: auto;
    color: var(--text-secondary);
}

/* === UTILITIES === */
.text-muted {
    color: var(--text-muted) !important;
}

.bg-light {
    background-color: var(--light) !important;
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
    .sidebar {
        margin-left: calc(-1 * var(--sidebar-width));
    }

    .sidebar.show {
        margin-left: 0;
    }

    .main-content {
        margin-left: 0;
    }

    .content-wrapper {
        padding: var(--spacing-md);
    }
}
