
:root {
    /* === PRIMARY COLORS === */
    --primary-color: #667eea;
    --primary-hover: #5a6fd8;
    --primary-active: #4c63d2;
    --primary-light: #e8ecff;
    --primary-dark: #3b4ec7;
    
    --secondary-color: #764ba2;
    --secondary-hover: #6a4190;
    --secondary-active: #5e377e;
    --secondary-light: #f0ebf8;
    --secondary-dark: #52306c;
    
    /* === ACCENT COLORS === */
    --accent-color: #f093fb;
    --accent-hover: #ee7ef9;
    --accent-active: #ec69f7;
    
    /* === STATUS COLORS === */
    --success-color: #28a745;
    --success-hover: #218838;
    --success-light: #d4edda;
    --success-dark: #155724;
    
    --warning-color: #ffc107;
    --warning-hover: #e0a800;
    --warning-light: #fff3cd;
    --warning-dark: #856404;
    
    --danger-color: #dc3545;
    --danger-hover: #c82333;
    --danger-light: #f8d7da;
    --danger-dark: #721c24;
    
    --info-color: #17a2b8;
    --info-hover: #138496;
    --info-light: #d1ecf1;
    --info-dark: #0c5460;
    
    /* === NEUTRAL COLORS === */
    --white: #ffffff;
    --light: #f8f9fa;
    --light-gray: #e9ecef;
    --gray: #6c757d;
    --dark-gray: #495057;
    --dark: #343a40;
    --black: #000000;
    
    /* === BACKGROUND COLORS === */
    --bg-primary: var(--white);
    --bg-secondary: var(--light);
    --bg-tertiary: #f5f6fa;
    --bg-dark: var(--dark);
    
    /* === TEXT COLORS === */
    --text-primary: var(--dark);
    --text-secondary: var(--gray);
    --text-muted: #6c757d;
    --text-light: var(--white);
    --text-link: #0077cc;
    --text-link-hover: #005fa3;
    
    /* === BORDER COLORS === */
    --border-color: #dee2e6;
    --border-light: #e5e5e5;
    --border-dark: #adb5bd;
    
    /* === SHADOW COLORS === */
    --shadow-light: rgba(0, 0, 0, 0.05);
    --shadow-medium: rgba(0, 0, 0, 0.1);
    --shadow-dark: rgba(0, 0, 0, 0.15);
    
    /* === TYPOGRAPHY === */
    --font-family-primary: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-family-secondary: 'Arial', sans-serif;
    --font-family-monospace: 'Courier New', monospace;
    
    /* === FONT SIZES === */
    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.875rem;   /* 14px */
    --font-size-base: 1rem;     /* 16px */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.25rem;    /* 20px */
    --font-size-2xl: 1.5rem;    /* 24px */
    --font-size-3xl: 1.875rem;  /* 30px */
    --font-size-4xl: 2.25rem;   /* 36px */
    
    /* === FONT WEIGHTS === */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    
    /* === LINE HEIGHTS === */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;
    
    /* === SPACING === */
    --spacing-xs: 0.25rem;   /* 4px */
    --spacing-sm: 0.5rem;    /* 8px */
    --spacing-md: 1rem;      /* 16px */
    --spacing-lg: 1.5rem;    /* 24px */
    --spacing-xl: 2rem;      /* 32px */
    --spacing-2xl: 3rem;     /* 48px */
    --spacing-3xl: 4rem;     /* 64px */
    
    /* === BORDER RADIUS === */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.375rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 0.75rem;
    --border-radius-2xl: 1rem;
    --border-radius-full: 9999px;
    
    /* === LAYOUT === */
    --sidebar-width: 250px;
    --header-height: 60px;
    --footer-height: 60px;
    
    /* === TRANSITIONS === */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* === Z-INDEX === */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* === DARK THEME VARIABLES === */
@media (prefers-color-scheme: dark) {
    :root {
        /* === BACKGROUND COLORS - DARK === */
        --bg-primary: #121212;
        --bg-secondary: #1e1e1e;
        --bg-tertiary: #2d2d2d;
        --bg-dark: #0a0a0a;

        /* === TEXT COLORS - DARK === */
        --text-primary: #ffffff;
        --text-secondary: #b3b3b3;
        --text-muted: #8a8a8a;
        --text-light: #ffffff;
        --text-link: #4fc3f7;
        --text-link-hover: #29b6f6;

        /* === BORDER COLORS - DARK === */
        --border-color: #404040;
        --border-light: #333333;
        --border-dark: #555555;

        /* === NEUTRAL COLORS - DARK === */
        --light: #2d2d2d;
        --light-gray: #404040;
        --gray: #8a8a8a;
        --dark-gray: #b3b3b3;
        --dark: #ffffff;

        /* === SHADOW COLORS - DARK === */
        --shadow-light: rgba(0, 0, 0, 0.3);
        --shadow-medium: rgba(0, 0, 0, 0.5);
        --shadow-dark: rgba(0, 0, 0, 0.7);

        /* === COMPONENT SPECIFIC - DARK === */
        --card-bg: #1e1e1e;
        --card-border: #404040;
        --card-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.4);

        --table-stripe-bg: rgba(255, 255, 255, 0.02);
        --table-hover-bg: rgba(255, 255, 255, 0.05);
        --table-border: #404040;

        --input-border: #404040;
        --input-focus-border: var(--primary-color);
        --input-focus-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.4);

        /* === STATUS COLORS - DARK VARIANTS === */
        --success-light: rgba(40, 167, 69, 0.2);
        --success-dark: #4caf50;

        --warning-light: rgba(255, 193, 7, 0.2);
        --warning-dark: #ffeb3b;

        --danger-light: rgba(220, 53, 69, 0.2);
        --danger-dark: #f44336;

        --info-light: rgba(23, 162, 184, 0.2);
        --info-dark: #2196f3;

        /* === SIDEBAR - DARK === */
        --sidebar-bg: linear-gradient(135deg, #1a1a2e, #16213e);
        --sidebar-text: #ffffff;
        --sidebar-hover: rgba(255, 255, 255, 0.15);
    }
}

/* === DARK THEME TOGGLE CLASS === */
[data-theme="dark"] {
    /* === BACKGROUND COLORS - DARK === */
    --bg-primary: #121212;
    --bg-secondary: #1e1e1e;
    --bg-tertiary: #2d2d2d;
    --bg-dark: #0a0a0a;

    /* === TEXT COLORS - DARK === */
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #8a8a8a;
    --text-light: #ffffff;
    --text-link: #4fc3f7;
    --text-link-hover: #29b6f6;

    /* === BORDER COLORS - DARK === */
    --border-color: #404040;
    --border-light: #333333;
    --border-dark: #555555;

    /* === NEUTRAL COLORS - DARK === */
    --light: #2d2d2d;
    --light-gray: #404040;
    --gray: #8a8a8a;
    --dark-gray: #b3b3b3;
    --dark: #ffffff;

    /* === SHADOW COLORS - DARK === */
    --shadow-light: rgba(0, 0, 0, 0.3);
    --shadow-medium: rgba(0, 0, 0, 0.5);
    --shadow-dark: rgba(0, 0, 0, 0.7);

    /* === COMPONENT SPECIFIC - DARK === */
    --card-bg: #1e1e1e;
    --card-border: #404040;
    --card-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.4);

    --table-stripe-bg: rgba(255, 255, 255, 0.02);
    --table-hover-bg: rgba(255, 255, 255, 0.05);
    --table-border: #404040;

    --input-border: #404040;
    --input-focus-border: var(--primary-color);
    --input-focus-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.4);

    /* === STATUS COLORS - DARK VARIANTS === */
    --success-light: rgba(40, 167, 69, 0.2);
    --success-dark: #4caf50;

    --warning-light: rgba(255, 193, 7, 0.2);
    --warning-dark: #ffeb3b;

    --danger-light: rgba(220, 53, 69, 0.2);
    --danger-dark: #f44336;

    --info-light: rgba(23, 162, 184, 0.2);
    --info-dark: #2196f3;

    /* === SIDEBAR - DARK === */
    --sidebar-bg: linear-gradient(135deg, #1a1a2e, #16213e);
    --sidebar-text: #ffffff;
    --sidebar-hover: rgba(255, 255, 255, 0.15);
}

/* === CUSTOM PROPERTIES FOR SPECIFIC COMPONENTS === */
:root {
    /* Sidebar */
    --sidebar-bg: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --sidebar-text: var(--white);
    --sidebar-hover: rgba(255, 255, 255, 0.1);

    /* Cards */
    --card-bg: var(--white);
    --card-border: var(--border-color);
    --card-shadow: 0 0.25rem 0.75rem var(--shadow-light);

    /* Buttons */
    --btn-shadow: 0 0.125rem 0.25rem var(--shadow-medium);
    --btn-shadow-hover: 0 0.25rem 0.5rem var(--shadow-medium);

    /* Tables */
    --table-stripe-bg: rgba(0, 0, 0, 0.02);
    --table-hover-bg: rgba(0, 0, 0, 0.05);
    --table-border: var(--border-light);

    /* Forms */
    --input-border: var(--border-color);
    --input-focus-border: var(--primary-color);
    --input-focus-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);

    /* Dark Mode Toggle */
    --toggle-bg: var(--light-gray);
    --toggle-thumb: var(--white);
    --toggle-active-bg: var(--primary-color);

    /* Scrollbar */
    --scrollbar-track: var(--light);
    --scrollbar-thumb: var(--gray);
    --scrollbar-thumb-hover: var(--dark-gray);
}
